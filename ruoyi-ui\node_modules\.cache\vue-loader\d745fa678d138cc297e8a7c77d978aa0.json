{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\ThemeSelection.vue?vue&type=style&index=0&id=e1aa7606&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\ThemeSelection.vue", "mtime": 1754981980278}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743599735798}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743599729946}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743599731247}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ThemeSelection.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsLA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "ThemeSelection.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"theme-selection-container\">\n    <div class=\"theme-header\">\n      <h2>选择主题风格</h2>\n      <p>为您的行业场景选择合适的主题风格</p>\n    </div>\n    \n    <div v-loading=\"loading && currentPage === 1\" class=\"theme-grid\" @scroll=\"handleScroll\" ref=\"themeGrid\">\n      <div \n        v-for=\"theme in themeOptions\" \n        :key=\"theme.themeId\"\n        class=\"theme-card\"\n        :class=\"{ 'selected': selectedTheme && selectedTheme.themeId === theme.themeId }\"\n        @click=\"selectTheme(theme)\"\n      >\n        <div class=\"theme-preview\">\n          <img :src=\"theme.themeEffectImg\" :alt=\"theme.themeName\" />\n          <div class=\"theme-overlay\">\n            <i class=\"el-icon-check\" v-if=\"selectedTheme && selectedTheme.themeId === theme.themeId\"></i>\n            <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(theme.themeEffectImg)\" title=\"预览大图\"></i>\n          </div>\n        </div>\n        <div class=\"theme-info\">\n          <h3>{{ theme.themeName }}</h3>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载更多提示 -->\n    <div v-if=\"loadingMore\" class=\"loading-more\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载中...</span>\n    </div>\n    \n    <!-- 没有更多数据提示 -->\n    <div v-if=\"!hasMore && themeOptions.length > 0\" class=\"no-more\">\n      <span>没有更多数据了</span>\n    </div>\n    \n    <div v-if=\"!loading && !themeOptions.length\" class=\"empty-state\">\n      <i class=\"el-icon-picture-outline\"></i>\n      <p>暂无主题数据</p>\n    </div>\n    \n    <div class=\"theme-actions\">\n      <el-button @click=\"$emit('cancel')\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection\" :disabled=\"!selectedTheme\">确认选择</el-button>\n    </div>\n\n    <!-- 图片预览对话框 -->\n    <el-dialog\n      :visible.sync=\"previewVisible\"\n      title=\"主题预览\"\n      width=\"60%\"\n      append-to-body\n      @close=\"closePreview\"\n    >\n      <div class=\"preview-container\">\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getThemeList } from '@/api/view/sceneView'\n\nexport default {\n  name: 'ThemeSelection',\n  data() {\n    return {\n      selectedTheme: null,\n      themeOptions: [],\n      loading: false,\n      loadingMore: false,\n      currentPage: 1,\n      pageSize: 20,\n      hasMore: true,\n      // 图片预览\n      previewVisible: false,\n      previewImageUrl: ''\n    }\n  },\n  created() {\n    this.loadThemeList()\n  },\n  methods: {\n    // 加载主题列表\n    async loadThemeList(isLoadMore = false) {\n      if (isLoadMore) {\n        this.loadingMore = true\n      } else {\n        this.loading = true\n        this.currentPage = 1\n        this.themeOptions = []\n        this.hasMore = true\n      }\n      \n      try {\n        const res = await getThemeList({\n          page: this.currentPage,\n          limit: this.pageSize\n        })\n        \n        if (res.code === 0 && Array.isArray(res.data)) {\n          if (isLoadMore) {\n            this.themeOptions = [...this.themeOptions, ...res.data]\n          } else {\n            this.themeOptions = res.data\n          }\n          \n          // 判断是否还有更多数据\n          this.hasMore = res.data.length === this.pageSize\n        } else {\n          this.$message.error(res.msg || '获取主题列表失败')\n        }\n      } catch (error) {\n        this.$message.error('获取主题列表失败')\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n      }\n    },\n    \n    // 滚动事件处理\n    handleScroll(event) {\n      const { scrollTop, scrollHeight, clientHeight } = event.target\n      \n      // 距离底部50px时触发加载\n      if (scrollTop + clientHeight >= scrollHeight - 50) {\n        this.loadMore()\n      }\n    },\n    \n    // 加载更多\n    async loadMore() {\n      if (this.loadingMore || !this.hasMore) {\n        return\n      }\n      \n      this.currentPage++\n      await this.loadThemeList(true)\n    },\n    \n    // 获取主题颜色（可以根据主题ID或名称设置不同颜色）\n    getThemeColor(theme) {\n      const colors = ['#409EFF', '#13ce66', '#f56c6c', '#909399', '#e6a23c']\n      const index = parseInt(theme.themeId) % colors.length\n      return colors[index]\n    },\n    \n    selectTheme(theme) {\n      this.selectedTheme = theme\n    },\n    \n    confirmSelection() {\n      if (!this.selectedTheme) {\n        this.$message.warning('请先选择一个主题')\n        return\n      }\n      \n      this.$emit('confirm', this.selectedTheme)\n    },\n    \n    // 图片预览\n    previewImage(url) {\n      if (url) {\n        this.previewImageUrl = url\n        this.previewVisible = true\n      }\n    },\n    \n    // 关闭预览\n    closePreview() {\n      this.previewVisible = false\n      this.previewImageUrl = ''\n    }\n  }\n}\n</script>\n\n<style scoped>\n.theme-selection-container {\n  padding: 10px 20px 20px 20px;\n  height: 600px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-header {\n  margin-bottom: 20px;\n}\n\n.theme-header h2 {\n  font-size: 24px;\n  color: #303133;\n  margin: 0 0 8px 0;\n}\n\n.theme-header p {\n  font-size: 14px;\n  color: #606266;\n  margin: 0;\n}\n\n.theme-grid {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  gap: 15px;\n  flex: 1;\n  overflow-y: auto;\n  padding-right: 10px;\n}\n\n.theme-card {\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: white;\n  height: 180px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-card:hover {\n  border-color: #409EFF;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n}\n\n.theme-card.selected {\n  border-color: #409EFF;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);\n}\n\n.theme-preview {\n  position: relative;\n  height: 120px;\n  overflow: hidden;\n}\n\n.theme-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.theme-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(128, 128, 128, 0.3);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.theme-card:hover .theme-overlay {\n  opacity: 1;\n}\n\n.theme-card.selected .theme-overlay {\n  opacity: 1;\n}\n\n.theme-overlay i {\n  font-size: 24px;\n  color: white;\n  margin: 0 5px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.theme-overlay i:hover {\n  transform: scale(1.2);\n}\n\n.theme-info {\n  padding: 10px;\n  text-align: left;\n  height: 60px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-info h3 {\n  font-size: 14px;\n  color: #303133;\n  margin: 0 0 4px 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.theme-info p {\n  font-size: 12px;\n  color: #606266;\n  line-height: 1.4;\n  margin: 0;\n  flex: 1;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  text-overflow: ellipsis;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  color: #909399;\n  font-size: 14px;\n}\n\n.loading-more i {\n  margin-right: 8px;\n}\n\n.no-more {\n  display: flex;\n  justify-content: center;\n  padding: 20px;\n  color: #c0c4cc;\n  font-size: 14px;\n}\n\n.theme-actions {\n  display: flex;\n  justify-content: center;\n  gap: 16px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n  flex: 1;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n/* 自定义滚动条样式 */\n.theme-grid::-webkit-scrollbar {\n  width: 6px;\n}\n\n.theme-grid::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.theme-grid::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.theme-grid::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.preview-container {\n  text-align: center;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"]}]}