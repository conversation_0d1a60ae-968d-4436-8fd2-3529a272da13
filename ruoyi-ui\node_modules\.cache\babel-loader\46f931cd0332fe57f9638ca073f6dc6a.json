{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\ThemeSelection.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\ThemeSelection.vue", "mtime": 1754981980278}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\babel.config.js", "mtime": 1753326339083}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_sceneView", "require", "name", "data", "selectedTheme", "themeOptions", "loading", "loadingMore", "currentPage", "pageSize", "hasMore", "previewVisible", "previewImageUrl", "created", "loadThemeList", "methods", "_arguments", "arguments", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "isLoadMore", "res", "wrap", "_callee$", "_context", "prev", "next", "length", "undefined", "getThemeList", "page", "limit", "sent", "code", "Array", "isArray", "concat", "_toConsumableArray2", "$message", "error", "msg", "t0", "finish", "stop", "handleScroll", "event", "_event$target", "target", "scrollTop", "scrollHeight", "clientHeight", "loadMore", "_this2", "_callee2", "_callee2$", "_context2", "abrupt", "getThemeColor", "theme", "colors", "index", "parseInt", "themeId", "selectTheme", "confirmSelection", "warning", "$emit", "previewImage", "url", "closePreview"], "sources": ["src/views/ThemeSelection.vue"], "sourcesContent": ["<template>\n  <div class=\"theme-selection-container\">\n    <div class=\"theme-header\">\n      <h2>选择主题风格</h2>\n      <p>为您的行业场景选择合适的主题风格</p>\n    </div>\n    \n    <div v-loading=\"loading && currentPage === 1\" class=\"theme-grid\" @scroll=\"handleScroll\" ref=\"themeGrid\">\n      <div \n        v-for=\"theme in themeOptions\" \n        :key=\"theme.themeId\"\n        class=\"theme-card\"\n        :class=\"{ 'selected': selectedTheme && selectedTheme.themeId === theme.themeId }\"\n        @click=\"selectTheme(theme)\"\n      >\n        <div class=\"theme-preview\">\n          <img :src=\"theme.themeEffectImg\" :alt=\"theme.themeName\" />\n          <div class=\"theme-overlay\">\n            <i class=\"el-icon-check\" v-if=\"selectedTheme && selectedTheme.themeId === theme.themeId\"></i>\n            <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(theme.themeEffectImg)\" title=\"预览大图\"></i>\n          </div>\n        </div>\n        <div class=\"theme-info\">\n          <h3>{{ theme.themeName }}</h3>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载更多提示 -->\n    <div v-if=\"loadingMore\" class=\"loading-more\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载中...</span>\n    </div>\n    \n    <!-- 没有更多数据提示 -->\n    <div v-if=\"!hasMore && themeOptions.length > 0\" class=\"no-more\">\n      <span>没有更多数据了</span>\n    </div>\n    \n    <div v-if=\"!loading && !themeOptions.length\" class=\"empty-state\">\n      <i class=\"el-icon-picture-outline\"></i>\n      <p>暂无主题数据</p>\n    </div>\n    \n    <div class=\"theme-actions\">\n      <el-button @click=\"$emit('cancel')\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection\" :disabled=\"!selectedTheme\">确认选择</el-button>\n    </div>\n\n    <!-- 图片预览对话框 -->\n    <el-dialog\n      :visible.sync=\"previewVisible\"\n      title=\"主题预览\"\n      width=\"60%\"\n      append-to-body\n      @close=\"closePreview\"\n    >\n      <div class=\"preview-container\">\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getThemeList } from '@/api/view/sceneView'\n\nexport default {\n  name: 'ThemeSelection',\n  data() {\n    return {\n      selectedTheme: null,\n      themeOptions: [],\n      loading: false,\n      loadingMore: false,\n      currentPage: 1,\n      pageSize: 20,\n      hasMore: true,\n      // 图片预览\n      previewVisible: false,\n      previewImageUrl: ''\n    }\n  },\n  created() {\n    this.loadThemeList()\n  },\n  methods: {\n    // 加载主题列表\n    async loadThemeList(isLoadMore = false) {\n      if (isLoadMore) {\n        this.loadingMore = true\n      } else {\n        this.loading = true\n        this.currentPage = 1\n        this.themeOptions = []\n        this.hasMore = true\n      }\n      \n      try {\n        const res = await getThemeList({\n          page: this.currentPage,\n          limit: this.pageSize\n        })\n        \n        if (res.code === 0 && Array.isArray(res.data)) {\n          if (isLoadMore) {\n            this.themeOptions = [...this.themeOptions, ...res.data]\n          } else {\n            this.themeOptions = res.data\n          }\n          \n          // 判断是否还有更多数据\n          this.hasMore = res.data.length === this.pageSize\n        } else {\n          this.$message.error(res.msg || '获取主题列表失败')\n        }\n      } catch (error) {\n        this.$message.error('获取主题列表失败')\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n      }\n    },\n    \n    // 滚动事件处理\n    handleScroll(event) {\n      const { scrollTop, scrollHeight, clientHeight } = event.target\n      \n      // 距离底部50px时触发加载\n      if (scrollTop + clientHeight >= scrollHeight - 50) {\n        this.loadMore()\n      }\n    },\n    \n    // 加载更多\n    async loadMore() {\n      if (this.loadingMore || !this.hasMore) {\n        return\n      }\n      \n      this.currentPage++\n      await this.loadThemeList(true)\n    },\n    \n    // 获取主题颜色（可以根据主题ID或名称设置不同颜色）\n    getThemeColor(theme) {\n      const colors = ['#409EFF', '#13ce66', '#f56c6c', '#909399', '#e6a23c']\n      const index = parseInt(theme.themeId) % colors.length\n      return colors[index]\n    },\n    \n    selectTheme(theme) {\n      this.selectedTheme = theme\n    },\n    \n    confirmSelection() {\n      if (!this.selectedTheme) {\n        this.$message.warning('请先选择一个主题')\n        return\n      }\n      \n      this.$emit('confirm', this.selectedTheme)\n    },\n    \n    // 图片预览\n    previewImage(url) {\n      if (url) {\n        this.previewImageUrl = url\n        this.previewVisible = true\n      }\n    },\n    \n    // 关闭预览\n    closePreview() {\n      this.previewVisible = false\n      this.previewImageUrl = ''\n    }\n  }\n}\n</script>\n\n<style scoped>\n.theme-selection-container {\n  padding: 10px 20px 20px 20px;\n  height: 600px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-header {\n  margin-bottom: 20px;\n}\n\n.theme-header h2 {\n  font-size: 24px;\n  color: #303133;\n  margin: 0 0 8px 0;\n}\n\n.theme-header p {\n  font-size: 14px;\n  color: #606266;\n  margin: 0;\n}\n\n.theme-grid {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  gap: 15px;\n  flex: 1;\n  overflow-y: auto;\n  padding-right: 10px;\n}\n\n.theme-card {\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: white;\n  height: 180px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-card:hover {\n  border-color: #409EFF;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n}\n\n.theme-card.selected {\n  border-color: #409EFF;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);\n}\n\n.theme-preview {\n  position: relative;\n  height: 120px;\n  overflow: hidden;\n}\n\n.theme-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.theme-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(128, 128, 128, 0.3);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.theme-card:hover .theme-overlay {\n  opacity: 1;\n}\n\n.theme-card.selected .theme-overlay {\n  opacity: 1;\n}\n\n.theme-overlay i {\n  font-size: 24px;\n  color: white;\n  margin: 0 5px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.theme-overlay i:hover {\n  transform: scale(1.2);\n}\n\n.theme-info {\n  padding: 10px;\n  text-align: left;\n  height: 60px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-info h3 {\n  font-size: 14px;\n  color: #303133;\n  margin: 0 0 4px 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.theme-info p {\n  font-size: 12px;\n  color: #606266;\n  line-height: 1.4;\n  margin: 0;\n  flex: 1;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  text-overflow: ellipsis;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  color: #909399;\n  font-size: 14px;\n}\n\n.loading-more i {\n  margin-right: 8px;\n}\n\n.no-more {\n  display: flex;\n  justify-content: center;\n  padding: 20px;\n  color: #c0c4cc;\n  font-size: 14px;\n}\n\n.theme-actions {\n  display: flex;\n  justify-content: center;\n  gap: 16px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n  flex: 1;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n/* 自定义滚动条样式 */\n.theme-grid::-webkit-scrollbar {\n  width: 6px;\n}\n\n.theme-grid::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.theme-grid::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.theme-grid::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.preview-container {\n  text-align: center;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"], "mappings": ";;;;;;;;;;;AAiEA,IAAAA,UAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,YAAA;MACAC,OAAA;MACAC,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,OAAA;MACA;MACAC,cAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,UAAA,GAAAC,SAAA;QAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,IAAAC,UAAA,EAAAC,GAAA;QAAA,WAAAJ,oBAAA,CAAAD,OAAA,IAAAM,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAN,UAAA,GAAAR,UAAA,CAAAe,MAAA,QAAAf,UAAA,QAAAgB,SAAA,GAAAhB,UAAA;cACA,IAAAQ,UAAA;gBACAN,KAAA,CAAAX,WAAA;cACA;gBACAW,KAAA,CAAAZ,OAAA;gBACAY,KAAA,CAAAV,WAAA;gBACAU,KAAA,CAAAb,YAAA;gBACAa,KAAA,CAAAR,OAAA;cACA;cAAAkB,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAGA,IAAAG,uBAAA;gBACAC,IAAA,EAAAhB,KAAA,CAAAV,WAAA;gBACA2B,KAAA,EAAAjB,KAAA,CAAAT;cACA;YAAA;cAHAgB,GAAA,GAAAG,QAAA,CAAAQ,IAAA;cAKA,IAAAX,GAAA,CAAAY,IAAA,UAAAC,KAAA,CAAAC,OAAA,CAAAd,GAAA,CAAAtB,IAAA;gBACA,IAAAqB,UAAA;kBACAN,KAAA,CAAAb,YAAA,MAAAmC,MAAA,KAAAC,mBAAA,CAAArB,OAAA,EAAAF,KAAA,CAAAb,YAAA,OAAAoC,mBAAA,CAAArB,OAAA,EAAAK,GAAA,CAAAtB,IAAA;gBACA;kBACAe,KAAA,CAAAb,YAAA,GAAAoB,GAAA,CAAAtB,IAAA;gBACA;;gBAEA;gBACAe,KAAA,CAAAR,OAAA,GAAAe,GAAA,CAAAtB,IAAA,CAAA4B,MAAA,KAAAb,KAAA,CAAAT,QAAA;cACA;gBACAS,KAAA,CAAAwB,QAAA,CAAAC,KAAA,CAAAlB,GAAA,CAAAmB,GAAA;cACA;cAAAhB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAiB,EAAA,GAAAjB,QAAA;cAEAV,KAAA,CAAAwB,QAAA,CAAAC,KAAA;YAAA;cAAAf,QAAA,CAAAC,IAAA;cAEAX,KAAA,CAAAZ,OAAA;cACAY,KAAA,CAAAX,WAAA;cAAA,OAAAqB,QAAA,CAAAkB,MAAA;YAAA;YAAA;cAAA,OAAAlB,QAAA,CAAAmB,IAAA;UAAA;QAAA,GAAAxB,OAAA;MAAA;IAEA;IAEA;IACAyB,YAAA,WAAAA,aAAAC,KAAA;MACA,IAAAC,aAAA,GAAAD,KAAA,CAAAE,MAAA;QAAAC,SAAA,GAAAF,aAAA,CAAAE,SAAA;QAAAC,YAAA,GAAAH,aAAA,CAAAG,YAAA;QAAAC,YAAA,GAAAJ,aAAA,CAAAI,YAAA;;MAEA;MACA,IAAAF,SAAA,GAAAE,YAAA,IAAAD,YAAA;QACA,KAAAE,QAAA;MACA;IACA;IAEA;IACAA,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAArC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAmC,SAAA;QAAA,WAAApC,oBAAA,CAAAD,OAAA,IAAAM,IAAA,UAAAgC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,IAAA,GAAA8B,SAAA,CAAA7B,IAAA;YAAA;cAAA,MACA0B,MAAA,CAAAjD,WAAA,KAAAiD,MAAA,CAAA9C,OAAA;gBAAAiD,SAAA,CAAA7B,IAAA;gBAAA;cAAA;cAAA,OAAA6B,SAAA,CAAAC,MAAA;YAAA;cAIAJ,MAAA,CAAAhD,WAAA;cAAAmD,SAAA,CAAA7B,IAAA;cAAA,OACA0B,MAAA,CAAA1C,aAAA;YAAA;YAAA;cAAA,OAAA6C,SAAA,CAAAZ,IAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IACA;IAEA;IACAI,aAAA,WAAAA,cAAAC,KAAA;MACA,IAAAC,MAAA;MACA,IAAAC,KAAA,GAAAC,QAAA,CAAAH,KAAA,CAAAI,OAAA,IAAAH,MAAA,CAAAhC,MAAA;MACA,OAAAgC,MAAA,CAAAC,KAAA;IACA;IAEAG,WAAA,WAAAA,YAAAL,KAAA;MACA,KAAA1D,aAAA,GAAA0D,KAAA;IACA;IAEAM,gBAAA,WAAAA,iBAAA;MACA,UAAAhE,aAAA;QACA,KAAAsC,QAAA,CAAA2B,OAAA;QACA;MACA;MAEA,KAAAC,KAAA,iBAAAlE,aAAA;IACA;IAEA;IACAmE,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAA5D,eAAA,GAAA4D,GAAA;QACA,KAAA7D,cAAA;MACA;IACA;IAEA;IACA8D,YAAA,WAAAA,aAAA;MACA,KAAA9D,cAAA;MACA,KAAAC,eAAA;IACA;EACA;AACA", "ignoreList": []}]}