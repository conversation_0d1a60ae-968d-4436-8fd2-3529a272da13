{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\ThemeSelection.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\ThemeSelection.vue", "mtime": 1754981980278}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ThemeSelection.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ThemeSelection.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"theme-selection-container\">\n    <div class=\"theme-header\">\n      <h2>选择主题风格</h2>\n      <p>为您的行业场景选择合适的主题风格</p>\n    </div>\n    \n    <div v-loading=\"loading && currentPage === 1\" class=\"theme-grid\" @scroll=\"handleScroll\" ref=\"themeGrid\">\n      <div \n        v-for=\"theme in themeOptions\" \n        :key=\"theme.themeId\"\n        class=\"theme-card\"\n        :class=\"{ 'selected': selectedTheme && selectedTheme.themeId === theme.themeId }\"\n        @click=\"selectTheme(theme)\"\n      >\n        <div class=\"theme-preview\">\n          <img :src=\"theme.themeEffectImg\" :alt=\"theme.themeName\" />\n          <div class=\"theme-overlay\">\n            <i class=\"el-icon-check\" v-if=\"selectedTheme && selectedTheme.themeId === theme.themeId\"></i>\n            <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(theme.themeEffectImg)\" title=\"预览大图\"></i>\n          </div>\n        </div>\n        <div class=\"theme-info\">\n          <h3>{{ theme.themeName }}</h3>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载更多提示 -->\n    <div v-if=\"loadingMore\" class=\"loading-more\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载中...</span>\n    </div>\n    \n    <!-- 没有更多数据提示 -->\n    <div v-if=\"!hasMore && themeOptions.length > 0\" class=\"no-more\">\n      <span>没有更多数据了</span>\n    </div>\n    \n    <div v-if=\"!loading && !themeOptions.length\" class=\"empty-state\">\n      <i class=\"el-icon-picture-outline\"></i>\n      <p>暂无主题数据</p>\n    </div>\n    \n    <div class=\"theme-actions\">\n      <el-button @click=\"$emit('cancel')\">取消</el-button>\n      <el-button type=\"primary\" @click=\"confirmSelection\" :disabled=\"!selectedTheme\">确认选择</el-button>\n    </div>\n\n    <!-- 图片预览对话框 -->\n    <el-dialog\n      :visible.sync=\"previewVisible\"\n      title=\"主题预览\"\n      width=\"60%\"\n      append-to-body\n      @close=\"closePreview\"\n    >\n      <div class=\"preview-container\">\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getThemeList } from '@/api/view/sceneView'\n\nexport default {\n  name: 'ThemeSelection',\n  data() {\n    return {\n      selectedTheme: null,\n      themeOptions: [],\n      loading: false,\n      loadingMore: false,\n      currentPage: 1,\n      pageSize: 20,\n      hasMore: true,\n      // 图片预览\n      previewVisible: false,\n      previewImageUrl: ''\n    }\n  },\n  created() {\n    this.loadThemeList()\n  },\n  methods: {\n    // 加载主题列表\n    async loadThemeList(isLoadMore = false) {\n      if (isLoadMore) {\n        this.loadingMore = true\n      } else {\n        this.loading = true\n        this.currentPage = 1\n        this.themeOptions = []\n        this.hasMore = true\n      }\n      \n      try {\n        const res = await getThemeList({\n          page: this.currentPage,\n          limit: this.pageSize\n        })\n        \n        if (res.code === 0 && Array.isArray(res.data)) {\n          if (isLoadMore) {\n            this.themeOptions = [...this.themeOptions, ...res.data]\n          } else {\n            this.themeOptions = res.data\n          }\n          \n          // 判断是否还有更多数据\n          this.hasMore = res.data.length === this.pageSize\n        } else {\n          this.$message.error(res.msg || '获取主题列表失败')\n        }\n      } catch (error) {\n        this.$message.error('获取主题列表失败')\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n      }\n    },\n    \n    // 滚动事件处理\n    handleScroll(event) {\n      const { scrollTop, scrollHeight, clientHeight } = event.target\n      \n      // 距离底部50px时触发加载\n      if (scrollTop + clientHeight >= scrollHeight - 50) {\n        this.loadMore()\n      }\n    },\n    \n    // 加载更多\n    async loadMore() {\n      if (this.loadingMore || !this.hasMore) {\n        return\n      }\n      \n      this.currentPage++\n      await this.loadThemeList(true)\n    },\n    \n    // 获取主题颜色（可以根据主题ID或名称设置不同颜色）\n    getThemeColor(theme) {\n      const colors = ['#409EFF', '#13ce66', '#f56c6c', '#909399', '#e6a23c']\n      const index = parseInt(theme.themeId) % colors.length\n      return colors[index]\n    },\n    \n    selectTheme(theme) {\n      this.selectedTheme = theme\n    },\n    \n    confirmSelection() {\n      if (!this.selectedTheme) {\n        this.$message.warning('请先选择一个主题')\n        return\n      }\n      \n      this.$emit('confirm', this.selectedTheme)\n    },\n    \n    // 图片预览\n    previewImage(url) {\n      if (url) {\n        this.previewImageUrl = url\n        this.previewVisible = true\n      }\n    },\n    \n    // 关闭预览\n    closePreview() {\n      this.previewVisible = false\n      this.previewImageUrl = ''\n    }\n  }\n}\n</script>\n\n<style scoped>\n.theme-selection-container {\n  padding: 10px 20px 20px 20px;\n  height: 600px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-header {\n  margin-bottom: 20px;\n}\n\n.theme-header h2 {\n  font-size: 24px;\n  color: #303133;\n  margin: 0 0 8px 0;\n}\n\n.theme-header p {\n  font-size: 14px;\n  color: #606266;\n  margin: 0;\n}\n\n.theme-grid {\n  display: grid;\n  grid-template-columns: repeat(7, 1fr);\n  gap: 15px;\n  flex: 1;\n  overflow-y: auto;\n  padding-right: 10px;\n}\n\n.theme-card {\n  border: 2px solid #e4e7ed;\n  border-radius: 8px;\n  overflow: hidden;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: white;\n  height: 180px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-card:hover {\n  border-color: #409EFF;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);\n}\n\n.theme-card.selected {\n  border-color: #409EFF;\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);\n}\n\n.theme-preview {\n  position: relative;\n  height: 120px;\n  overflow: hidden;\n}\n\n.theme-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.theme-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(128, 128, 128, 0.3);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.theme-card:hover .theme-overlay {\n  opacity: 1;\n}\n\n.theme-card.selected .theme-overlay {\n  opacity: 1;\n}\n\n.theme-overlay i {\n  font-size: 24px;\n  color: white;\n  margin: 0 5px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.theme-overlay i:hover {\n  transform: scale(1.2);\n}\n\n.theme-info {\n  padding: 10px;\n  text-align: left;\n  height: 60px;\n  display: flex;\n  flex-direction: column;\n}\n\n.theme-info h3 {\n  font-size: 14px;\n  color: #303133;\n  margin: 0 0 4px 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.theme-info p {\n  font-size: 12px;\n  color: #606266;\n  line-height: 1.4;\n  margin: 0;\n  flex: 1;\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  text-overflow: ellipsis;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  color: #909399;\n  font-size: 14px;\n}\n\n.loading-more i {\n  margin-right: 8px;\n}\n\n.no-more {\n  display: flex;\n  justify-content: center;\n  padding: 20px;\n  color: #c0c4cc;\n  font-size: 14px;\n}\n\n.theme-actions {\n  display: flex;\n  justify-content: center;\n  gap: 16px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n  margin-top: 20px;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n  flex: 1;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n/* 自定义滚动条样式 */\n.theme-grid::-webkit-scrollbar {\n  width: 6px;\n}\n\n.theme-grid::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.theme-grid::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.theme-grid::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.preview-container {\n  text-align: center;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n}\n</style>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"]}]}