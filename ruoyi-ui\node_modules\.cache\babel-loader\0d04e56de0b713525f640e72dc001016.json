{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\src\\views\\index.vue", "mtime": 1754982261752}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\babel.config.js", "mtime": 1753326339083}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743599737981}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743599736169}, {"path": "C:\\Users\\<USER>\\Desktop\\数学馆\\huluwa_server_root\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743599728288}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_industry", "require", "_SceneConfigNode", "_interopRequireDefault", "_sceneView", "_NetworkPlanConfig", "_BusinessValueConfig", "_VrSceneConfig", "_ThemeSelectionDialog", "_axios", "name", "components", "SceneConfigNode", "NetworkPlanConfig", "BusinessValueConfig", "VrSceneConfig", "ThemeSelectionDialog", "data", "_this", "menuData", "flatMenuData", "activeMenu", "industryCode", "selectedTheme", "form", "mainTitle", "subTitle", "bgImgUrl", "bgFileUrl", "panoramicViewXmlUrl", "sceneConfigTree", "selectedNode", "loading", "switchingIndustry", "rules", "introduceVideoImgUrl", "required", "message", "trigger", "introduceVideoFileUrl", "videoExplanationFileUrl", "uploadingType", "uploadingKey", "categories", "introduceVideo", "status", "backgroundImgFileUrl", "backgroundFileUrl", "videoExplanation", "videoSegmentedVoList", "sceneTreeOptions", "sceneCascaderProps", "label", "value", "children", "emitPath", "checkStrictly", "disabled", "isSelected", "some", "seg", "sceneId", "id", "bgFileList", "videoExplanationFileList", "xmlFileList", "networkPlanDataMap", "businessValueDataMap", "vrSceneDataMap", "previewVisible", "previewImageUrl", "searchKeyword", "treeExpandedKeys", "uploadModes", "bgFile", "synchronizing", "submitting", "computed", "videoSegmentedList", "length", "time", "scene<PERSON><PERSON>", "sceneCode", "networkPlanData", "get", "networkVideoList", "videoExplanationVo", "set", "$set", "businessValueData", "vrSceneData", "val", "filteredMenuData", "_this2", "filterTree", "nodes", "map", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchesSearch", "toLowerCase", "includes", "_objectSpread2", "default", "filter", "Boolean", "created", "initTokenFromUrl", "loadIndustryMenu", "methods", "urlParams", "URLSearchParams", "window", "location", "search", "token", "localStorage", "setItem", "axios", "defaults", "headers", "common", "concat", "console", "log", "storedToken", "getItem", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "getIndustryList", "sent", "code", "Array", "isArray", "plate", "<PERSON><PERSON><PERSON>", "plateName", "type", "industryTreeListVos", "industry", "industryName", "for<PERSON>ach", "_this3$flatMenuData", "push", "apply", "_toConsumableArray2", "String", "$nextTick", "$refs", "menuTree", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSelect", "stop", "handleTreeNodeClick", "handleSearch", "highlightText", "text", "regex", "RegExp", "replace", "_arguments", "arguments", "_this4", "_callee2", "keepSelectedNode", "showLoading", "currentSelectedNode", "currentIndustry", "sceneCategory", "videoData", "nodeToSelect", "networkData", "_callee2$", "_context2", "undefined", "document", "body", "style", "overflow", "contentPanel", "querySelector", "scrollTop", "loadSceneTreeOptions", "find", "item", "getSceneViewConfig", "sceneViewConfigId", "updateBgFileList", "updateXmlFileList", "themeInfoVo", "themeId", "themeName", "themeEffectImg", "remark", "sceneDefaultConfigVoList", "configItem", "key", "keyName", "enabled", "keyValue", "editing", "editingName", "originalName", "classification", "defaultStatus", "industrySceneInfoVo", "findSceneIdByCode", "updateVideoExplanationFileList", "sceneListVo", "adaptSceneTree", "findNodeById", "networkSolutionVo", "commercialValueListVo", "vrInfoListVo", "t0", "error", "$message", "finish", "handleBeforeUpload", "file", "addSegment", "scene", "removeSegment", "index", "splice", "beforeUploadIntroduceImg", "_this5", "_callee3", "formData", "_callee3$", "_context3", "startsWith", "abrupt", "$modal", "FormData", "append", "uploadSceneFile", "fileUrl", "success", "msg", "closeLoading", "beforeUploadIntroduceVideo", "_this6", "_callee4", "fileName", "_formData", "_res", "_fileName", "_callee4$", "_context4", "split", "pop", "url", "uid", "Date", "now", "imgUrl", "endsWith", "introduceVideoFileList", "t1", "beforeUploadExplanationVideo", "addSceneConfigNode", "parentId", "newNode", "parentNode", "removeSceneConfigNode", "nodeId", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "found", "err", "e", "f", "addScenePainPoint", "painPoints", "title", "contents", "showTime", "removeScenePainPoint", "idx", "addScenePainContent", "removeScenePainContent", "cidx", "addSceneCostContent", "removeSceneCostContent", "beforeUploadSceneConfigImg", "_this7", "reader", "FileReader", "onload", "target", "result", "readAsDataURL", "beforeUploadSceneConfigFile", "handleSceneNodeClick", "rawTree", "_this8", "parent", "arr", "adapted", "sceneInfoId", "x", "y", "isUnfold", "displayLocation", "treeClassification", "introduceVideoVo", "viewInfoId", "tradition", "sceneTraditionVo", "panoramicViewXmlKey", "backgroundResources", "sceneVideoList", "v", "tag", "coordinates", "sceneFileRelList", "rel", "fileId", "clickX", "clickY", "wide", "high", "xmlKey", "bindSceneCode", "bgImg", "painPointList", "p", "painPointId", "bigTitle", "content", "displayTime", "wisdom5g", "scene5gVo", "costEstimate", "costEstimationInfoVo", "handleSubmit", "_this9", "_callee5", "_this9$networkPlanDat", "_this9$networkPlanDat2", "_this9$networkPlanDat3", "_this9$networkPlanDat4", "currentSelectedNodeId", "currentSelectedNodeName", "submitData", "response", "pathIds", "<PERSON><PERSON><PERSON>", "_callee5$", "_context5", "industryId", "networkSolutionInfoVo", "plan", "cat", "baseConfig", "convertedSceneList", "convertSceneTreeToApi", "commercialValueDTO", "vrInfoDtoList", "vr", "address", "sceneViewUpd", "msgSuccess", "targetId", "currentPath", "_iterator2", "_step2", "newPath", "slice", "addVideoSegment", "removeVideoSegment", "getDeepTreeOptions", "tree", "_this0", "_this1", "_callee6", "_callee6$", "_context6", "getSceneTreeList", "handleSceneCascaderChange", "findScene", "_iterator3", "_step3", "isSceneDisabled", "currentIdx", "sceneTree", "_this10", "paramId", "resource", "coord", "pain", "handleTimeChange", "findInTree", "_iterator4", "_step4", "handleRemoveBgFile", "fileList", "handleRemoveIntroduceVideoFile", "updateIntroduceVideoFileList", "handleRemoveVideoExplanationFile", "handleRemoveXmlFile", "previewImage", "closePreview", "deleteBgImage", "_this11", "$confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "beforeUploadXmlFile", "_this12", "_callee7", "_callee7$", "_context7", "size", "onThemeChange", "theme", "defaultBgImage", "formatCoordinatesForSubmit", "xValues", "join", "yV<PERSON><PERSON>", "parseCoordinatesFromApi", "xArray", "yArray", "max<PERSON><PERSON><PERSON>", "Math", "max", "i", "startEditTitle", "_this13", "category", "inputRef", "focus", "select", "finishEditTitle", "trim", "cancelEditTitle", "setUploadMode", "mode", "handleBgFileUrlInput", "handleVideoExplanationUrlInput", "handleIntroduceVideoUrlInput", "handleSynchronizeFile", "_this14", "_callee8", "_callee8$", "_context8", "warning", "synchronizationFile"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-container\">\r\n    <!-- ✅ 左侧菜单区域 -->\r\n    <div class=\"menu-panel\">\r\n      <!-- 搜索框 -->\r\n      <div class=\"menu-search\">\r\n        <el-input\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"搜索菜单...\"\r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n          @input=\"handleSearch\"\r\n        />\r\n      </div>\r\n      \r\n      <el-tree\r\n        ref=\"menuTree\"\r\n        :data=\"filteredMenuData\"\r\n        :props=\"{ label: 'name', children: 'children' }\"\r\n        node-key=\"id\"\r\n        :current-node-key=\"activeMenu\"\r\n        @node-click=\"handleTreeNodeClick\"\r\n        highlight-current\r\n        :expand-on-click-node=\"false\"\r\n        :default-expanded-keys=\"menuData.map(item => item.id)\"\r\n        class=\"menu-tree\"\r\n      >\r\n        <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\r\n          <span v-html=\"highlightText(data.name)\"></span>\r\n        </span>\r\n      </el-tree>\r\n    </div>\r\n\r\n    <!-- ✅ 右侧内容区域 -->\r\n    <div class=\"content-panel\" :class=\"{ 'loading-no-scroll': switchingIndustry }\" v-loading=\"switchingIndustry\" element-loading-text=\"正在切换行业...\">\r\n      <el-form :model=\"form\" ref=\"sceneForm\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 左侧：基本信息 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block\" shadow=\"never\">\r\n              <div slot=\"header\">基本信息</div>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主标题\" required>\r\n                    <el-input v-model=\"form.mainTitle\" placeholder=\"请输入主标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"副标题\" required>\r\n                    <el-input v-model=\"form.subTitle\" placeholder=\"请输入副标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景图片首帧\">\r\n                    <el-upload\r\n                      class=\"upload image-upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"false\"\r\n                      list-type=\"picture-card\"\r\n                      accept=\"image/*\"\r\n                      :before-upload=\"beforeUploadIntroduceImg\"\r\n                      :http-request=\"() => {}\"\r\n                    >\r\n                      <div v-if=\"form.bgImgUrl\" class=\"image-preview-container\">\r\n                        <img :src=\"form.bgImgUrl\" class=\"upload-image\" />\r\n                        <div class=\"image-overlay\">\r\n                          <i class=\"el-icon-zoom-in preview-icon\" @click.stop=\"previewImage(form.bgImgUrl)\" title=\"预览\"></i>\r\n                          <i class=\"el-icon-delete delete-icon\" @click.stop=\"deleteBgImage\" title=\"删除\"></i>\r\n                        </div>\r\n                      </div>\r\n                      <i v-else class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"主题选择\">\r\n                    <theme-selection-dialog \r\n                      v-model=\"selectedTheme\"\r\n                      @change=\"onThemeChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"背景文件\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.bgFile || 'upload'\" @input=\"value => setUploadMode('bgFile', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.bgFile || 'upload') === 'upload'\"\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"bgFileList\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file)\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveBgFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传背景文件</el-button>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"form.bgFileUrl\"\r\n                      placeholder=\"请输入文件链接\"\r\n                      @input=\"handleBgFileUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"XML文件\">\r\n                    <el-upload\r\n                      class=\"upload\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"xmlFileList\"\r\n                      accept=\".xml\"\r\n                      :before-upload=\"beforeUploadXmlFile\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveXmlFile\"\r\n                    >\r\n                      <el-button type=\"primary\">上传XML文件</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传xml文件，且不超过50MB</div>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-card>\r\n          </el-col>\r\n          \r\n          <!-- 右侧：视频讲解 -->\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"mini-block video-card\" shadow=\"never\">\r\n              <div slot=\"header\" class=\"video-header-row\">\r\n                <span>视频讲解</span>\r\n                <el-switch v-model=\"videoExplanation.status\" :active-value=\"'0'\" :inactive-value=\"'1'\" style=\"float:right;\" />\r\n              </div>\r\n              <div v-show=\"videoExplanation.status === '0'\">\r\n              <el-collapse-transition>\r\n                <div class=\"video-card-yu\">\r\n                  <el-form-item label=\"讲解视频\">\r\n                    <div style=\"margin-bottom: 8px;\">\r\n                      <el-radio-group v-model=\"uploadModes.videoExplanation || 'upload'\" @input=\"value => setUploadMode('videoExplanation', value)\" size=\"small\">\r\n                        <el-radio-button label=\"upload\">上传文件</el-radio-button>\r\n                        <el-radio-button label=\"url\">填写链接</el-radio-button>\r\n                      </el-radio-group>\r\n                    </div>\r\n                    \r\n                    <!-- 上传模式 -->\r\n                    <el-upload\r\n                      v-if=\"(uploadModes.videoExplanation || 'upload') === 'upload'\"\r\n                      action=\"#\"\r\n                      :show-file-list=\"true\"\r\n                      :file-list=\"videoExplanationFileList\"\r\n                      accept=\".mp4\"\r\n                      :before-upload=\"file => beforeUploadIntroduceVideo(file, 'videoExplanation', 'backgroundFileUrl')\"\r\n                      :http-request=\"() => {}\"\r\n                      :on-remove=\"handleRemoveVideoExplanationFile\"\r\n                    >\r\n                      <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n                      <div slot=\"tip\" class=\"el-upload__tip\">只能上传mp4文件</div>\r\n                    </el-upload>\r\n                    \r\n                    <!-- 链接模式 -->\r\n                    <el-input\r\n                      v-else\r\n                      v-model=\"videoExplanation.backgroundFileUrl\"\r\n                      placeholder=\"请输入视频链接\"\r\n                      @input=\"handleVideoExplanationUrlInput\"\r\n                    />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"视频分段说明\">\r\n                    <div class=\"segment-scroll\">\r\n                      <div v-for=\"(seg, idx) in videoSegmentedList\" :key=\"idx\" style=\"display:flex;align-items:center;margin-bottom:8px;\">\r\n                        <el-input-number\r\n                          v-model=\"seg.time\"\r\n                          :min=\"0\"\r\n                          :max=\"999999\"\r\n                          placeholder=\"时间\"\r\n                          style=\"width: 120px; margin-right: 10px;\"\r\n                          @change=\"val => handleTimeChange(val, idx)\"\r\n                        />\r\n                        <span style=\"margin-right: 10px; color: #606266;\">秒</span>\r\n                        <el-cascader\r\n                          v-model=\"seg.sceneId\"\r\n                          :options=\"sceneTreeOptions\"\r\n                          :props=\"sceneCascaderProps\"\r\n                          filterable\r\n                          check-strictly\r\n                          placeholder=\"所属场景\"\r\n                          style=\"width: 200px; margin-right: 10px;\"\r\n                          @change=\"val => handleSceneCascaderChange(val, idx)\"\r\n                        />\r\n                        <el-button type=\"danger\" icon=\"el-icon-delete\" circle @click=\"removeVideoSegment(idx)\" />\r\n                      </div>\r\n                    </div>\r\n                    <el-button type=\"primary\" plain @click=\"addVideoSegment\" style=\"margin-top: 8px;\">增加分段</el-button>\r\n                  </el-form-item>\r\n                </div>\r\n              </el-collapse-transition>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <!-- ✅ 分类区域（支持折叠） -->\r\n      <div\r\n        v-for=\"(category, index) in categories\"\r\n        :key=\"category.key\"\r\n        class=\"category-block\"\r\n      >\r\n        <div class=\"category-header\">\r\n          <div class=\"category-title\" @dblclick=\"startEditTitle(index)\">\r\n            <el-input\r\n              v-if=\"category.editing\"\r\n              v-model=\"category.editingName\"\r\n              @blur=\"finishEditTitle(index)\"\r\n              @keyup.enter=\"finishEditTitle(index)\"\r\n              @keyup.esc=\"cancelEditTitle(index)\"\r\n              :data-edit-index=\"index\"\r\n              size=\"small\"\r\n              style=\"width: 200px;\"\r\n              placeholder=\"请输入标题\"\r\n            />\r\n            <span v-else>{{ category.name }}</span>\r\n          </div>\r\n          <el-switch\r\n            v-model=\"category.enabled\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ccc\"\r\n          />\r\n        </div>\r\n\r\n        <div v-show=\"category.enabled\" class=\"category-body\">\r\n          <div v-if=\"category.key === 'default_scene'\">\r\n            <el-form label-width=\"120px\">\r\n              <el-row :gutter=\"20\">\r\n                <el-col :span=\"24\">\r\n                  <!-- 场景配置分块 -->\r\n                  <el-card class=\"mini-block scene-config-container\" shadow=\"never\" style=\"margin-top: 20px;\">\r\n                    <div slot=\"header\">场景配置</div>\r\n                    <div>\r\n                      <el-row :gutter=\"20\">\r\n                        <el-col :span=\"6\">\r\n                          <el-tree\r\n                            ref=\"sceneTree\"\r\n                            :data=\"sceneConfigTree\"\r\n                            node-key=\"id\"\r\n                            :props=\"{ label: 'name', children: 'children' }\"\r\n                            @node-click=\"handleSceneNodeClick\"\r\n                            highlight-current\r\n                            :expand-on-click-node=\"false\"\r\n                            :default-expanded-keys=\"treeExpandedKeys.length > 0 ? treeExpandedKeys : (sceneConfigTree.length ? [sceneConfigTree[0].id] : [])\"\r\n                            :current-node-key=\"selectedNode ? selectedNode.id : null\"\r\n                            :sort=\"false\"\r\n                          />\r\n                        </el-col>\r\n                        <el-col :span=\"18\">\r\n                          <SceneConfigNode \r\n                            v-if=\"selectedNode\" \r\n                            :node=\"selectedNode\" \r\n                            :root-tree=\"sceneConfigTree\"\r\n                            :scene-tree-options=\"sceneTreeOptions\"\r\n                            :left-tree-industry-code=\"industryCode\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </div>\r\n                  </el-card>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n\r\n          <!-- 其他分类的占位内容 -->\r\n          <div v-else-if=\"category.key === 'default_plan'\">\r\n            <network-plan-config v-model=\"networkPlanData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_value'\">\r\n            <business-value-config v-model=\"businessValueData\" :left-tree-industry-code=\"industryCode\" />\r\n          </div>\r\n          <div v-else-if=\"category.key === 'default_vr'\">\r\n            <vr-scene-config v-model=\"vrSceneData\" />\r\n          </div>\r\n          <div v-else>\r\n            <p>这里是 <strong>{{ category.name }}</strong> 分类的内容区域。</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit-footer\">\r\n      <div class=\"form-actions\">\r\n        <!-- 同步文件按钮加 tooltip -->\r\n        <el-tooltip \r\n          effect=\"dark\" \r\n          content=\"链接填写完成后，点击【提交】后再点击【同步】按钮\" \r\n          placement=\"top\"\r\n        >\r\n          <el-button \r\n            type=\"success\" \r\n            @click=\"handleSynchronizeFile\" \r\n            :disabled=\"!form.sceneViewConfigId\"\r\n            :loading=\"synchronizing\"\r\n          >\r\n            {{ synchronizing ? '同步中...' : '同步文件' }}\r\n          </el-button>\r\n        </el-tooltip>\r\n\r\n        <el-button \r\n          type=\"primary\" \r\n          @click=\"handleSubmit\" \r\n          :loading=\"submitting\" \r\n          style=\"margin-left: 30px;\"\r\n        >\r\n          {{ submitting ? '提交中...' : '提交' }}\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    </div>\r\n    \r\n    <!-- 图片预览对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"previewVisible\"\r\n      title=\"图片预览\"\r\n      width=\"60%\"\r\n      append-to-body\r\n      @close=\"closePreview\"\r\n    >\r\n      <div class=\"preview-container\">\r\n        <img :src=\"previewImageUrl\" class=\"preview-image\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getIndustryList, getSceneTreeList } from '@/api/view/industry'\r\nimport SceneConfigNode from './SceneConfigNode.vue'\r\nimport { getSceneViewConfig, sceneViewUpd, uploadSceneFile, synchronizationFile } from '@/api/view/sceneView'\r\nimport NetworkPlanConfig from './NetworkPlanConfig.vue'\r\nimport BusinessValueConfig from './BusinessValueConfig.vue'\r\nimport VrSceneConfig from './VrSceneConfig.vue'\r\nimport ThemeSelectionDialog from './ThemeSelectionDialog.vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'IndustryScenePage',\r\n  components: {\r\n    SceneConfigNode,\r\n    NetworkPlanConfig,\r\n    BusinessValueConfig,\r\n    VrSceneConfig,\r\n    ThemeSelectionDialog\r\n  },\r\n  data() {\r\n    return {\r\n      menuData: [], // 原始菜单数据\r\n      flatMenuData: [], // 扁平化的行业数据，用于搜索和业务逻辑\r\n      activeMenu: '',\r\n      industryCode: '',\r\n      selectedTheme: null, // 当前选择的主题\r\n      form: {\r\n        mainTitle: '',\r\n        subTitle: '',\r\n        bgImgUrl: '',\r\n        bgFileUrl: '',\r\n        panoramicViewXmlUrl: ''\r\n      },\r\n      sceneConfigTree: [],\r\n      selectedNode: null,\r\n      loading: false, // 页面加载状态\r\n      switchingIndustry: false, // 新增：切换行业的loading状态\r\n      rules: {\r\n        introduceVideoImgUrl: [\r\n          { required: true, message: '请上传介绍视频首帧', trigger: 'change' }\r\n        ],\r\n        introduceVideoFileUrl: [\r\n          { required: true, message: '请上传介绍视频', trigger: 'change' }\r\n        ],\r\n        videoExplanationFileUrl: [\r\n          { required: true, message: '请上传讲解视频', trigger: 'change' }\r\n        ]\r\n      },\r\n      uploadingType: '',\r\n      uploadingKey: '',\r\n      categories: [], // 改为空数组，从后端动态获取\r\n      introduceVideo: {\r\n        status: '0',\r\n        backgroundImgFileUrl: '',\r\n        backgroundFileUrl: ''\r\n      },\r\n      videoExplanation: {\r\n        status: '0',\r\n        backgroundFileUrl: '',\r\n        videoSegmentedVoList: []\r\n      },\r\n      sceneTreeOptions: [],\r\n      sceneCascaderProps: {\r\n        label: 'sceneName',\r\n        value: 'id',\r\n        children: 'children',\r\n        emitPath: false,\r\n        checkStrictly: true,\r\n        disabled: (data) => {\r\n          // 允许所有节点可选，只要没有被其他分段选中\r\n          const isSelected = this.videoExplanation && this.videoExplanation.videoSegmentedVoList\r\n            ? this.videoExplanation.videoSegmentedVoList.some(seg => seg.sceneId === data.id)\r\n            : false\r\n          return isSelected\r\n        }\r\n      },\r\n      bgFileList: [], // 背景文件列表\r\n      videoExplanationFileList: [], // 讲解视频文件列表\r\n      xmlFileList: [], // XML文件列表\r\n      networkPlanDataMap: {}, // 改为对象，按菜单ID存储\r\n      businessValueDataMap: {}, // 商业价值数据映射\r\n      vrSceneDataMap: {}, // VR看现场数据映射\r\n      // 图片预览\r\n      previewVisible: false,\r\n      previewImageUrl: '',\r\n      searchKeyword: '',\r\n      treeExpandedKeys: [], // 新增：保存树的展开状态\r\n      uploadModes: {\r\n        bgFile: 'upload',\r\n        videoExplanation: 'upload',\r\n        introduceVideo: 'upload'\r\n      },\r\n      synchronizing: false,\r\n      submitting: false // 添加这个属性\r\n    }\r\n  },\r\n  computed: {\r\n    videoSegmentedList() {\r\n      // 如果没有数据，默认返回一行空数据\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        return [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      return this.videoExplanation.videoSegmentedVoList\r\n    },\r\n    networkPlanData: {\r\n      get() {\r\n        const menuData = this.networkPlanDataMap[this.activeMenu]\r\n        if (!menuData) {\r\n          return {\r\n            networkVideoList: [],\r\n            videoExplanationVo: {\r\n              status: '0',\r\n              backgroundFileUrl: '',\r\n              videoSegmentedVoList: []\r\n            }\r\n          }\r\n        }\r\n        return menuData\r\n      },\r\n      set(value) {\r\n        this.$set(this.networkPlanDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    businessValueData: {\r\n      get() {\r\n        return this.businessValueDataMap[this.activeMenu] || []\r\n      },\r\n      set(value) {\r\n        this.$set(this.businessValueDataMap, this.activeMenu, value)\r\n      }\r\n    },\r\n    vrSceneData: {\r\n      get() {\r\n        return this.vrSceneDataMap[this.activeMenu] || []\r\n      },\r\n      set(val) {\r\n        this.$set(this.vrSceneDataMap, this.activeMenu, val)\r\n      }\r\n    },\r\n    filteredMenuData() {\r\n      if (!this.searchKeyword) {\r\n        return this.menuData\r\n      }\r\n      \r\n      // 递归过滤树形数据\r\n      const filterTree = (nodes) => {\r\n        return nodes.map(node => {\r\n          const filteredChildren = node.children ? filterTree(node.children) : []\r\n          const matchesSearch = node.name && node.name.toLowerCase().includes(this.searchKeyword.toLowerCase())\r\n          \r\n          if (matchesSearch || filteredChildren.length > 0) {\r\n            return {\r\n              ...node,\r\n              children: filteredChildren\r\n            }\r\n          }\r\n          return null\r\n        }).filter(Boolean)\r\n      }\r\n      \r\n      return filterTree(this.menuData)\r\n    }\r\n  },\r\n  created() {\r\n    // 从URL获取token并设置\r\n    this.initTokenFromUrl()\r\n    this.loadIndustryMenu()\r\n  },\r\n  methods: {\r\n    // 从URL获取token并设置\r\n    initTokenFromUrl() {\r\n      const urlParams = new URLSearchParams(window.location.search)\r\n  const token = urlParams.get('token')\r\n  \r\n  if (token) {\r\n    localStorage.setItem('external-token', token)\r\n    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    console.log('从URL获取到token:', token)\r\n  } else {\r\n    const storedToken = localStorage.getItem('external-token')\r\n    if (storedToken) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`\r\n    }\r\n  }\r\n    },\r\n    async loadIndustryMenu() {\r\n      const res = await getIndustryList()\r\n      if (res.code === 0 && Array.isArray(res.data)) {\r\n        // 转换数据结构为树形菜单\r\n        this.menuData = res.data.map(plate => ({\r\n          id: `plate_${plate.plateKey}`,\r\n          name: plate.plateName,\r\n          type: 'plate',\r\n          children: plate.industryTreeListVos ? plate.industryTreeListVos.map(industry => ({\r\n            id: industry.id,\r\n            name: industry.industryName,\r\n            industryCode: industry.industryCode,\r\n            plate: industry.plate,\r\n            type: 'industry'\r\n          })) : []\r\n        }))\r\n        \r\n        // 创建扁平化的行业数据，用于业务逻辑\r\n        this.flatMenuData = []\r\n        res.data.forEach(plate => {\r\n          if (plate.industryTreeListVos) {\r\n            this.flatMenuData.push(...plate.industryTreeListVos)\r\n          }\r\n        })\r\n        \r\n        // 默认选中第一个行业\r\n        if (this.flatMenuData.length) {\r\n          this.activeMenu = String(this.flatMenuData[0].id)\r\n          this.industryCode = this.flatMenuData[0].industryCode\r\n          // 等待DOM更新后设置树组件的当前节点\r\n          this.$nextTick(() => {\r\n            // 确保树组件已渲染并设置当前选中节点\r\n            if (this.$refs.menuTree && this.$refs.menuTree.setCurrentKey) {\r\n              this.$refs.menuTree.setCurrentKey(this.activeMenu)\r\n            }\r\n          })\r\n          \r\n          // 加载第一个行业的数据\r\n          await this.handleSelect(this.activeMenu)\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleTreeNodeClick(data) {\r\n      // 只有点击行业节点才处理\r\n      if (data.type === 'industry') {\r\n        this.handleSelect(String(data.id))\r\n        this.industryCode = data.industryCode;\r\n      }\r\n    },\r\n\r\n    handleSearch(value) {\r\n      this.searchKeyword = value\r\n    },\r\n    highlightText(text) {\r\n      if (!this.searchKeyword) return text\r\n      const regex = new RegExp(`(${this.searchKeyword})`, 'gi')\r\n      return text.replace(regex, '<span class=\"highlight\">$1</span>')\r\n    },\r\n    async handleSelect(id, keepSelectedNode = false, showLoading = true) {\r\n      try {\r\n        // 开启切换行业的loading\r\n        if (showLoading) {\r\n          this.switchingIndustry = true\r\n          // 禁用页面滚动\r\n          document.body.style.overflow = 'hidden'\r\n\r\n          // 右侧编辑栏回到顶部\r\n          this.$nextTick(() => {\r\n            const contentPanel = document.querySelector('.content-panel')\r\n            if (contentPanel) {\r\n              contentPanel.scrollTop = 0\r\n            }\r\n          })\r\n        }\r\n        \r\n        this.activeMenu = id\r\n        await this.loadSceneTreeOptions(this.activeMenu)\r\n        \r\n        // 保存当前选中的节点\r\n        const currentSelectedNode = keepSelectedNode ? this.selectedNode : null\r\n        \r\n        // 重置主题选择\r\n        this.selectedTheme = null\r\n        \r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneViewConfig({ industryCode: industryCode })\r\n        if (res.code === 0 && res.data) {\r\n          \r\n          // 同步主标题、副标题、背景图片、XML文件等\r\n          this.form.sceneViewConfigId = res.data.sceneViewConfigId || ''\r\n          this.form.mainTitle = res.data.mainTitle || ''\r\n          this.form.subTitle = res.data.subTitle || ''\r\n          this.form.bgImgUrl = res.data.backgroundImgFileUrl || ''\r\n          this.form.bgFileUrl = res.data.backgroundFileUrl || ''\r\n          this.form.panoramicViewXmlUrl = res.data.panoramicViewXmlUrl || ''\r\n          \r\n          // 更新背景文件列表\r\n          this.updateBgFileList()\r\n          \r\n          // 更新XML文件列表\r\n          this.updateXmlFileList()\r\n          \r\n          // 回显主题选择\r\n          if (res.data.themeInfoVo) {\r\n            this.selectedTheme = {\r\n              themeId: res.data.themeInfoVo.themeId,\r\n              themeName: res.data.themeInfoVo.themeName,\r\n              themeEffectImg: res.data.themeInfoVo.themeEffectImg,\r\n              remark: res.data.themeInfoVo.remark\r\n            }\r\n          } else {\r\n            this.selectedTheme = null\r\n          }\r\n          \r\n          // 处理 sceneDefaultConfigVoList，动态生成 categories\r\n          if (res.data.sceneDefaultConfigVoList && Array.isArray(res.data.sceneDefaultConfigVoList)) {\r\n            this.categories = res.data.sceneDefaultConfigVoList.map(configItem => ({\r\n              id: configItem.id,\r\n              key: configItem.keyName,\r\n              name: configItem.name,\r\n              enabled: configItem.keyValue === '0', // keyValue为'0'表示启用\r\n              editing: false,\r\n              editingName: '',\r\n              originalName: configItem.name,\r\n              remark: configItem.remark,\r\n              classification: configItem.classification,\r\n              defaultStatus: configItem.defaultStatus\r\n            }))\r\n            \r\n            // 查找场景配置分类\r\n            const sceneCategory = res.data.sceneDefaultConfigVoList.find(item => item.keyName === 'default_scene')\r\n            \r\n            // 处理视频讲解数据\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.videoExplanationVo) {\r\n              const videoData = sceneCategory.industrySceneInfoVo.videoExplanationVo\r\n              this.videoExplanation = {\r\n                status: videoData.status || '0',\r\n                backgroundFileUrl: videoData.backgroundFileUrl || '',\r\n                videoSegmentedVoList: videoData.videoSegmentedVoList ? videoData.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time,\r\n                  sceneCode: seg.sceneCode,\r\n                  sceneName: seg.sceneName,\r\n                  sceneId: this.findSceneIdByCode(seg.sceneCode) // 根据sceneCode查找sceneId\r\n                })) : []\r\n              }\r\n            } else {\r\n              this.videoExplanation = {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            \r\n            // 更新视频讲解文件列表\r\n            this.updateVideoExplanationFileList()\r\n            \r\n            // 处理场景配置树\r\n            if (sceneCategory && sceneCategory.industrySceneInfoVo && sceneCategory.industrySceneInfoVo.sceneListVo) {\r\n              this.sceneConfigTree = this.adaptSceneTree(sceneCategory.industrySceneInfoVo.sceneListVo)\r\n              \r\n              // 如果需要保持选中节点\r\n              if (keepSelectedNode && currentSelectedNode) {\r\n                const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNode.id)\r\n                if (nodeToSelect) {\r\n                  this.selectedNode = nodeToSelect\r\n                } else {\r\n                  this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n                }\r\n              } else {\r\n                // 默认选择第一个节点\r\n                this.selectedNode = this.sceneConfigTree.length > 0 ? this.sceneConfigTree[0] : null\r\n              }\r\n            } else {\r\n              // 没有场景数据时清空\r\n              this.sceneConfigTree = []\r\n              this.selectedNode = null\r\n            }\r\n          }\r\n          \r\n          // 处理网络方案数据\r\n          if (res.data.networkSolutionVo) {\r\n            const networkData = {\r\n              networkVideoList: res.data.networkSolutionVo.networkVideoList || [],\r\n              videoExplanationVo: res.data.networkSolutionVo.videoExplanationVo || {\r\n                status: '0',\r\n                backgroundFileUrl: '',\r\n                videoSegmentedVoList: []\r\n              }\r\n            }\r\n            this.$set(this.networkPlanDataMap, this.activeMenu, networkData)\r\n          }\r\n\r\n          // 处理商业价值数据\r\n          if (res.data.commercialValueListVo) {\r\n            this.$set(this.businessValueDataMap, this.activeMenu, res.data.commercialValueListVo)\r\n          }\r\n\r\n          // 处理VR看现场数据\r\n          if (res.data.vrInfoListVo) {\r\n            this.$set(this.vrSceneDataMap, this.activeMenu, res.data.vrInfoListVo)\r\n          }\r\n          \r\n          // 其他数据处理逻辑保持不变...\r\n        }\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n        this.$message.error('加载数据失败')\r\n      } finally {\r\n        // 关闭切换行业的loading\r\n        if (showLoading) {\r\n          this.switchingIndustry = false\r\n          // 恢复页面滚动\r\n          document.body.style.overflow = ''\r\n        }\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n      return false // 拦截默认上传行为\r\n    },\r\n    addSegment() {\r\n      this.form.videoSegmentedVoList.push({ time: '', scene: '' })\r\n    },\r\n    removeSegment(index) {\r\n      if (this.form.videoSegmentedVoList.length >= 1) {\r\n        this.form.videoSegmentedVoList.splice(index, 1)\r\n      }\r\n    },\r\n    async beforeUploadIntroduceImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传图片，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传，单独上传图片时使用 fileUrl\r\n            this[type][key] = res.data.fileUrl\r\n          } else {\r\n            // 针对主背景图片的上传\r\n            this.form.bgImgUrl = res.data.fileUrl\r\n          }\r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    async beforeUploadIntroduceVideo(file, type, key) {\r\n      // 如果是主背景文件上传（没有type和key参数）\r\n      if (!type && !key) {\r\n        try {\r\n          this.$modal.loading(\"正在上传文件，请稍候...\")\r\n          const formData = new FormData()\r\n          formData.append('file', file)\r\n          formData.append('industryCode', this.industryCode)\r\n          \r\n          const res = await uploadSceneFile(formData)\r\n          if (res.code === 0 && res.data) {\r\n            // 设置背景文件URL\r\n            this.form.bgFileUrl = res.data.fileUrl\r\n            \r\n            // 直接覆盖背景文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            this.bgFileList = [{\r\n              name: fileName,\r\n              url: res.data.fileUrl,\r\n              uid: Date.now()\r\n            }]\r\n            \r\n            // 如果是MP4文件且返回了imgUrl，自动设置背景图片首帧\r\n            if (file.type === 'video/mp4' && res.data.imgUrl) {\r\n              this.form.bgImgUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成背景图片首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || '上传失败')\r\n          }\r\n        } catch (error) {\r\n          this.$message.error('上传失败')\r\n        } finally {\r\n          this.$modal.closeLoading()\r\n        }\r\n        return false\r\n      }\r\n      \r\n      // 其他视频上传逻辑（介绍视频、讲解视频等）\r\n      if (!file.type.startsWith('video/') && !file.name.endsWith('.mp4')) {\r\n        this.$message.error('只能上传MP4视频文件！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传视频，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          if (type && key) {\r\n            // 针对介绍视频和视频讲解的上传\r\n            this[type][key] = res.data.fileUrl\r\n            \r\n            // 直接覆盖对应的文件列表\r\n            const fileName = res.data.fileUrl.split('/').pop()\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl') {\r\n              this.introduceVideoFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            } else if (type === 'videoExplanation' && key === 'backgroundFileUrl') {\r\n              this.videoExplanationFileList = [{\r\n                name: fileName,\r\n                url: res.data.fileUrl,\r\n                uid: Date.now()\r\n              }]\r\n            }\r\n            \r\n            // 如果是介绍视频上传，且返回了imgUrl，自动设置介绍视频首帧\r\n            if (type === 'introduceVideo' && key === 'backgroundFileUrl' && res.data.imgUrl) {\r\n              this.introduceVideo.backgroundImgFileUrl = res.data.imgUrl\r\n              this.$message.success('上传成功，已自动生成介绍视频首帧')\r\n            } else {\r\n              this.$message.success('上传成功')\r\n            }\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    beforeUploadExplanationVideo(file) {\r\n      this.uploadingType = 'mp4'\r\n      this.uploadingKey = 'videoExplanationFileUrl'\r\n      return this.handleBeforeUpload(file)\r\n    },\r\n    // 新增方法：添加场景配置节点\r\n    addSceneConfigNode(parentId = null) {\r\n      const newNode = {\r\n        id: Date.now(), // 生成唯一ID\r\n        name: '新场景',\r\n        type: 'scene', // 类型为场景\r\n        enabled: true,\r\n        children: [],\r\n        parentId: parentId\r\n      }\r\n      if (parentId) {\r\n        const parentNode = this.findNodeById(this.sceneConfigTree, parentId)\r\n        if (parentNode) {\r\n          parentNode.children.push(newNode)\r\n        }\r\n      } else {\r\n        this.sceneConfigTree.push(newNode)\r\n      }\r\n      return newNode.id\r\n    },\r\n    // 新增方法：删除场景配置节点\r\n    removeSceneConfigNode(nodeId) {\r\n      this.sceneConfigTree = this.sceneConfigTree.filter(node => node.id !== nodeId)\r\n    },\r\n    // 新增方法：查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) {\r\n          return node\r\n        }\r\n        if (node.children && node.children.length > 0) {\r\n          const found = this.findNodeById(node.children, id)\r\n          if (found) {\r\n            return found\r\n          }\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    // 新增方法：添加场景的痛点价值\r\n    addScenePainPoint(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.push({ title: '', contents: [''], showTime: '' })\r\n      }\r\n    },\r\n    // 新增方法：删除场景的痛点价值\r\n    removeScenePainPoint(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints.splice(idx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景痛点内容的项\r\n    addScenePainContent(nodeId, idx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景痛点内容的项\r\n    removeScenePainContent(nodeId, idx, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'scene') {\r\n        node.painPoints = node.painPoints || []\r\n        node.painPoints[idx].contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：添加场景的成本预估内容\r\n    addSceneCostContent(nodeId) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.push('')\r\n      }\r\n    },\r\n    // 新增方法：删除场景的成本预估内容\r\n    removeSceneCostContent(nodeId, cidx) {\r\n      const node = this.findNodeById(this.sceneConfigTree, nodeId)\r\n      if (node && node.type === 'costEstimate') {\r\n        node.contents = node.contents || []\r\n        node.contents.splice(cidx, 1)\r\n      }\r\n    },\r\n    // 新增方法：上传场景配置图片\r\n    beforeUploadSceneConfigImg(file, type, key) {\r\n      if (!file.type.startsWith('image/')) {\r\n        this.$message.error('只能上传图片文件！')\r\n        return false\r\n      }\r\n      const reader = new FileReader()\r\n      reader.onload = e => {\r\n        this.findNodeById(this.sceneConfigTree, type)[key] = e.target.result\r\n      }\r\n      reader.readAsDataURL(file)\r\n      return false\r\n    },\r\n    // 新增方法：上传场景配置文件\r\n    beforeUploadSceneConfigFile(file, type, key) {\r\n      // 这里只做文件名回显\r\n      this.findNodeById(this.sceneConfigTree, type)[key] = file.name\r\n      return false\r\n    },\r\n    handleSceneNodeClick(node) {\r\n      this.selectedNode = node\r\n    },\r\n    // 适配函数移到methods中，供接口数据适配使用\r\n    adaptSceneTree(rawTree, parent = null) {\r\n      if (!rawTree) return []\r\n      const arr = Array.isArray(rawTree) ? rawTree : [rawTree]\r\n      return arr.map((node) => {\r\n        const adapted = {\r\n          id: node.sceneId,\r\n          sceneInfoId: node.sceneInfoId || null,\r\n          name: node.sceneName,\r\n          code: node.sceneCode,\r\n          x: node.x,\r\n          y: node.y,\r\n          type: node.type,\r\n          status: node.status !== null ? node.status : '0',\r\n          isUnfold: node.isUnfold !== null && node.isUnfold !== undefined ? node.isUnfold : '1',\r\n          displayLocation: node.displayLocation !== null && node.displayLocation !== undefined ? node.displayLocation : '0',\r\n          treeClassification: node.treeClassification !== null && node.treeClassification !== undefined ? node.treeClassification : '3',\r\n          introduceVideoVo: node.introduceVideoVo ? {\r\n            id: node.introduceVideoVo.id || '',\r\n            type: node.introduceVideoVo.type || '',\r\n            viewInfoId: node.introduceVideoVo.viewInfoId || '',\r\n            status: node.introduceVideoVo.status || '0',\r\n            backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || '',\r\n            backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || ''\r\n          } : { id: '', type: '', viewInfoId: '', status: '0', backgroundImgFileUrl: '', backgroundFileUrl: '' },\r\n          tradition: node.sceneTraditionVo ? {\r\n            name: node.sceneTraditionVo.name || '',\r\n            panoramicViewXmlKey: node.sceneTraditionVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.sceneTraditionVo.sceneVideoList ? \r\n              node.sceneTraditionVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                label: v.tag || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }],\r\n                wide: v.wide || 0,\r\n                high: v.high || 0,\r\n                bgImg: v.backgroundImgFileUrl || '',\r\n                bgFile: v.backgroundFileUrl || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || ''\r\n              })) : [],\r\n            painPoints: node.sceneTraditionVo.painPointList ?\r\n              (Array.isArray(node.sceneTraditionVo.painPointList) ? node.sceneTraditionVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          wisdom5g: node.scene5gVo ? {\r\n            name: node.scene5gVo.name || '',\r\n            panoramicViewXmlKey: node.scene5gVo.panoramicViewXmlKey || '',\r\n            backgroundResources: node.scene5gVo.sceneVideoList ? \r\n              node.scene5gVo.sceneVideoList.map(v => ({\r\n                id: v.id || null,\r\n                tag: v.tag || '',\r\n                status: v.status || '',\r\n                type: v.type || '',\r\n                viewInfoId: v.viewInfoId || '',\r\n                backgroundImgFileUrl: v.backgroundImgFileUrl || '',\r\n                backgroundFileUrl: v.backgroundFileUrl || '',\r\n                coordinates: v.sceneFileRelList ? v.sceneFileRelList.map(rel => ({\r\n                  id: rel.id || null,\r\n                  fileId: rel.fileId || null,\r\n                  x: rel.clickX || '',\r\n                  y: rel.clickY || '',\r\n                  wide: rel.wide || '',\r\n                  high: rel.high || '',\r\n                  xmlKey: rel.xmlKey || '',\r\n                  sceneId: this.findSceneIdByCode(rel.bindSceneCode),\r\n                  sceneCode: rel.bindSceneCode || ''\r\n                })) : [{ id: null, fileId: null, x: '', y: '', wide: '', high: '', sceneId: '', sceneCode: '' }]\r\n              })) : [],\r\n            painPoints: node.scene5gVo.painPointList ? \r\n              (Array.isArray(node.scene5gVo.painPointList) ? node.scene5gVo.painPointList.map(p => ({\r\n                painPointId: p.painPointId || null,\r\n                title: p.bigTitle || '',\r\n                contents: p.content || [],\r\n                showTime: p.displayTime || ''\r\n              })) : []) : []\r\n          } : { name: '', panoramicViewXmlKey: '', backgroundResources: [], painPoints: [] },\r\n          costEstimate: node.costEstimationInfoVo ? {\r\n            painPointId: node.costEstimationInfoVo.painPointId || null,\r\n            status: node.costEstimationInfoVo.status || '0',\r\n            title: node.costEstimationInfoVo.bigTitle || '',\r\n            contents: node.costEstimationInfoVo.content || []\r\n          } : { painPointId: null, status: '0', title: '', contents: [] },\r\n          children: [],\r\n          parent\r\n        }\r\n        // 递归处理子节点，保持后端返回的原始顺序\r\n        adapted.children = node.children ? this.adaptSceneTree(node.children, adapted) : []\r\n        return adapted\r\n      })\r\n    },\r\n    async handleSubmit() {\r\n      try {\r\n        this.submitting = true\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === this.activeMenu)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        // 保存当前选中的节点的完整信息\r\n        const currentSelectedNodeId = this.selectedNode ? this.selectedNode.id : null\r\n        const currentSelectedNodeName = this.selectedNode ? this.selectedNode.name : null\r\n        \r\n        // 构建提交数据\r\n        const submitData = {\r\n          industryId: this.activeMenu,\r\n          industryCode: industryCode, // 新增参数\r\n          sceneViewConfigId: this.form.sceneViewConfigId || null,\r\n          mainTitle: this.form.mainTitle || null,\r\n          subTitle: this.form.subTitle || null,\r\n          themeId: this.selectedTheme ? this.selectedTheme.themeId : null,\r\n          backgroundImgFileUrl: this.form.bgImgUrl || null,\r\n          backgroundFileUrl: this.form.bgFileUrl || null,\r\n          panoramicViewXmlUrl: this.form.panoramicViewXmlUrl || null,\r\n          networkSolutionInfoVo: {\r\n            networkVideoList: (this.networkPlanDataMap[this.activeMenu]?.networkVideoList && Array.isArray(this.networkPlanDataMap[this.activeMenu].networkVideoList)) ? \r\n              this.networkPlanDataMap[this.activeMenu].networkVideoList.map(plan => ({\r\n                id: plan.id || null,\r\n                type: 4,\r\n                tag: plan.tag || null,\r\n                clickX: plan.clickX || null,\r\n                clickY: plan.clickY || null,\r\n                wide: plan.wide || null,\r\n                high: plan.high || null,\r\n                backgroundImgFileUrl: plan.backgroundImgFileUrl || null,\r\n                backgroundFileUrl: plan.backgroundFileUrl || null,\r\n                status: null,\r\n                viewInfoId: null\r\n              })) : [],\r\n            videoExplanationVo: {\r\n              status: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.status || '0',\r\n              backgroundFileUrl: this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.backgroundFileUrl || null,\r\n              videoSegmentedVoList: (this.networkPlanDataMap[this.activeMenu]?.videoExplanationVo?.videoSegmentedVoList?.length) ? \r\n                this.networkPlanDataMap[this.activeMenu].videoExplanationVo.videoSegmentedVoList.map(seg => ({\r\n                  time: seg.time || null,\r\n                  sceneCode: seg.sceneCode || null,\r\n                  sceneName: seg.sceneName || null\r\n                })) : null\r\n            }\r\n          },\r\n          sceneDefaultConfigVoList: this.categories.map(cat => {\r\n            const baseConfig = {\r\n              id: cat.id || null,\r\n              industryId: this.activeMenu || null,\r\n              name: cat.name,\r\n              keyName: cat.key,\r\n              keyValue: cat.enabled ? '0' : '1',\r\n              remark: cat.remark || cat.name,\r\n              defaultStatus: '0'\r\n            }\r\n\r\n            if (cat.key === 'default_scene') {\r\n              const convertedSceneList = this.convertSceneTreeToApi(this.sceneConfigTree)     \r\n              baseConfig.industrySceneInfoVo = {\r\n                videoExplanationVo: {\r\n                  status: this.videoExplanation.status,\r\n                  backgroundFileUrl: this.videoExplanation.backgroundFileUrl || null,\r\n                  videoSegmentedVoList: this.videoExplanation.videoSegmentedVoList.length ? this.videoExplanation.videoSegmentedVoList : null\r\n                },\r\n                sceneListVo: convertedSceneList\r\n              }\r\n            } else {\r\n              baseConfig.industrySceneInfoVo = null\r\n            }\r\n            \r\n            return baseConfig\r\n          }),\r\n          commercialValueDTO: (this.businessValueDataMap[this.activeMenu] && Array.isArray(this.businessValueDataMap[this.activeMenu])) ? \r\n            this.businessValueDataMap[this.activeMenu].map(value => ({\r\n              id: value.id || null,\r\n              viewInfoId: value.viewInfoId || null,\r\n              type: 5,\r\n              tag: value.tag || null,\r\n              backgroundImgFileUrl: value.backgroundImgFileUrl || null,\r\n              backgroundFileUrl: value.backgroundFileUrl || null\r\n            })) : [],\r\n          vrInfoDtoList: (this.vrSceneDataMap[this.activeMenu] && Array.isArray(this.vrSceneDataMap[this.activeMenu])) ? \r\n            this.vrSceneDataMap[this.activeMenu]. map(vr => ({\r\n              id: vr.id || null,\r\n              industryId: vr.industryId || this.activeMenu,\r\n              type: vr.type || 6,\r\n              viewInfoId: vr.viewInfoId || null,\r\n              name: vr.name || '',\r\n              address: vr.address || ''\r\n            })) : [],\r\n        }\r\n      \r\n        const response = await sceneViewUpd(submitData)\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n\r\n        // 重新加载数据（不显示全局loading，避免与按钮loading冲突）\r\n        await this.handleSelect(this.activeMenu, false, false)\r\n\r\n        if (currentSelectedNodeId && this.sceneConfigTree.length > 0) {\r\n          // 强制查找并设置选中节点\r\n          const nodeToSelect = this.findNodeById(this.sceneConfigTree, currentSelectedNodeId)\r\n\r\n          if (nodeToSelect) {\r\n            // 计算并设置展开路径\r\n            const pathIds = []\r\n            const findPath = (nodes, targetId, currentPath = []) => {\r\n              for (const node of nodes) {\r\n                const newPath = [...currentPath, node.id]\r\n                if (node.id === targetId) {\r\n                  pathIds.push(...newPath)\r\n                  return true\r\n                }\r\n                if (node.children && node.children.length > 0) {\r\n                  if (findPath(node.children, targetId, newPath)) {\r\n                    return true\r\n                  }\r\n                }\r\n              }\r\n              return false\r\n            }\r\n\r\n            findPath(this.sceneConfigTree, currentSelectedNodeId)\r\n            this.treeExpandedKeys = pathIds.slice(0, -1)\r\n\r\n            // 先设置选中节点\r\n            this.selectedNode = nodeToSelect\r\n\r\n            // 强制更新树组件的选中状态\r\n            this.$nextTick(() => {\r\n              this.$nextTick(() => {\r\n                // 模拟点击节点来强制更新选中状态\r\n                this.handleSceneNodeClick(nodeToSelect)\r\n              })\r\n            })\r\n          }\r\n        }\r\n        \r\n        console.log('提交内容:', submitData)\r\n      } catch (error) {\r\n        console.error('提交失败:', error)\r\n        this.$message.error('提交失败')\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    },\r\n    addVideoSegment() {\r\n      // 如果原数组为空，先初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: '', sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      this.videoExplanation.videoSegmentedVoList.push({ time: '', sceneId: '', sceneName: '', sceneCode: '' })\r\n    },\r\n    removeVideoSegment(idx) {\r\n      this.videoExplanation.videoSegmentedVoList.splice(idx, 1)\r\n    },\r\n    //递归重构结构\r\n    getDeepTreeOptions(tree) {\r\n    return tree.map(item => {\r\n      // 复制当前节点的基础属性\r\n      const node = { ...item };\r\n      \r\n      // 如果存在 children 且不为空，则递归处理\r\n      if (node.children && node.children.length > 0) {\r\n        node.children = this.getDeepTreeOptions(node.children);\r\n      } else {\r\n        // 当 children 为空或不存在时，删除 children 属性（可选）\r\n        delete node.children;\r\n      }\r\n      \r\n      return node;\r\n    });\r\n  },\r\n    async loadSceneTreeOptions(id) {\r\n      try {\r\n        // 从扁平化菜单数据中获取当前行业的 industryCode\r\n        const currentIndustry = this.flatMenuData.find(item => String(item.id) === id)\r\n        const industryCode = currentIndustry ? currentIndustry.industryCode : null\r\n        \r\n        const res = await getSceneTreeList({ industryCode: industryCode })\r\n        if (res.code === 0 && Array.isArray(res.data)) {\r\n          this.sceneTreeOptions = this.getDeepTreeOptions(res.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('加载场景树失败:', error)\r\n      }\r\n    },\r\n    handleSceneCascaderChange(val, idx) {\r\n      // 确保数组和索引位置的对象存在\r\n      if (!this.videoExplanation.videoSegmentedVoList || !this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        return\r\n      }\r\n      \r\n      const findScene = (tree, id) => {\r\n        for (const node of tree) {\r\n          if (node.id === id) return node\r\n          if (node.children && node.children.length) {\r\n            const found = findScene(node.children, id)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      const node = findScene(this.sceneTreeOptions, val)\r\n      if (node) {\r\n        // 设置场景ID和相关信息\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneId = val\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneName = node.sceneName\r\n        this.videoExplanation.videoSegmentedVoList[idx].sceneCode = node.sceneCode\r\n      }\r\n    },\r\n    isSceneDisabled(id, currentIdx) {\r\n      // 除当前分段外，其他分段已选的id\r\n      return this.videoExplanation.videoSegmentedVoList.some((seg, idx) => idx !== currentIdx && seg.sceneId === id)\r\n    },\r\n    // 将场景树转换为接口格式\r\n    convertSceneTreeToApi(sceneTree) {\r\n      console.log(\"提交的数据:\", sceneTree);\r\n      return sceneTree.map(node => ({\r\n        sceneInfoId: node.sceneInfoId || null,\r\n        sceneId: node.id,\r\n        paramId: node.parent ? node.parent.id : null,\r\n        sceneName: node.name,\r\n        sceneCode: node.code,\r\n        x: node.x || null,\r\n        y: node.y || null,\r\n        type: node.type || null,\r\n        status: node.status,\r\n        isUnfold: (node.children && node.children.length > 0) ? (node.isUnfold || '1') : null,\r\n        displayLocation: (node.children && node.children.length > 0) ? (node.displayLocation || '0') : null,\r\n        treeClassification: (node.children && node.children.length > 0) ? (node.treeClassification || '3') : null,\r\n        introduceVideoVo: node.introduceVideoVo ? {\r\n          id: node.introduceVideoVo.id || null,\r\n          type: node.introduceVideoVo.type || null,\r\n          viewInfoId: node.introduceVideoVo.viewInfoId || null,\r\n          status: node.introduceVideoVo.status || null,\r\n          backgroundImgFileUrl: node.introduceVideoVo.backgroundImgFileUrl || null,\r\n          backgroundFileUrl: node.introduceVideoVo.backgroundFileUrl || null\r\n        } : null,\r\n        sceneTraditionVo: node.tradition ? {\r\n          name: node.tradition.name || null,\r\n          panoramicViewXmlKey: node.tradition.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.tradition.backgroundResources && node.tradition.backgroundResources.length ? \r\n            node.tradition.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.label || null,\r\n              wide: resource.wide || null,\r\n              high: resource.high || null,\r\n              status: resource.status || null,\r\n              type: 1,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.tradition.painPoints && node.tradition.painPoints.length ? \r\n            node.tradition.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || '0'\r\n            })) : null\r\n        } : null,\r\n        scene5gVo: node.wisdom5g ? {\r\n          name: node.wisdom5g.name || null,\r\n          panoramicViewXmlKey: node.wisdom5g.panoramicViewXmlKey || null,\r\n          sceneVideoList: node.wisdom5g.backgroundResources && node.wisdom5g.backgroundResources.length ? \r\n            node.wisdom5g.backgroundResources.map(resource => ({\r\n              id: resource.id || null,\r\n              tag: resource.tag || null,\r\n              status: resource.status || null,\r\n              type: 2,\r\n              viewInfoId: resource.viewInfoId || null,\r\n              backgroundImgFileUrl: resource.bgImg || '',\r\n              backgroundFileUrl: resource.bgFile || '',\r\n              sceneFileRelList: resource.coordinates && resource.coordinates.length ? \r\n                resource.coordinates.map(coord => ({\r\n                  id: coord.id || null,\r\n                  fileId: coord.fileId || null,\r\n                  clickX: coord.x !== undefined && coord.x !== null ? (coord.x === '' ? '' : coord.x) : null,\r\n                  clickY: coord.y !== undefined && coord.y !== null ? (coord.y === '' ? '' : coord.y) : null,\r\n                  wide: coord.wide !== undefined && coord.wide !== null ? (coord.wide === '' || coord.wide === 0 ? '' : coord.wide) : null,\r\n                  high: coord.high !== undefined && coord.high !== null ? (coord.high === '' || coord.high === 0 ? '' : coord.high) : null,\r\n                  xmlKey: coord.xmlKey !== undefined && coord.xmlKey !== null ? (coord.xmlKey === '' ? '' : coord.xmlKey) : null,\r\n                  bindSceneCode: coord.sceneCode !== undefined && coord.sceneCode !== null ? (coord.sceneCode === '' ? '' : coord.sceneCode) : null\r\n                })) : []\r\n            })) : null,\r\n          painPointList: node.wisdom5g.painPoints && node.wisdom5g.painPoints.length ? \r\n            node.wisdom5g.painPoints.map(pain => ({\r\n              painPointId: pain.painPointId || null,\r\n              bigTitle: pain.title || null,\r\n              content: pain.contents || [],\r\n              displayTime: pain.showTime || '0'\r\n            })) : null\r\n        } : null,\r\n        costEstimationInfoVo: node.costEstimate ? {\r\n          painPointId: node.costEstimate.painPointId || null,\r\n          status: node.costEstimate.status || '0',\r\n          bigTitle: node.costEstimate.title || null,\r\n          content: node.costEstimate.contents && node.costEstimate.contents.length ? node.costEstimate.contents : null\r\n        } : null,\r\n        children: node.children && node.children.length ? this.convertSceneTreeToApi(node.children) : []\r\n      }))\r\n    },\r\n    handleTimeChange(val, idx) {\r\n      // 确保数组已初始化\r\n      if (!this.videoExplanation.videoSegmentedVoList || this.videoExplanation.videoSegmentedVoList.length === 0) {\r\n        this.videoExplanation.videoSegmentedVoList = [{ time: 0, sceneId: '', sceneName: '', sceneCode: '' }]\r\n      }\r\n      // 更新对应位置的时间值\r\n      if (this.videoExplanation.videoSegmentedVoList[idx]) {\r\n        this.videoExplanation.videoSegmentedVoList[idx].time = val\r\n      }\r\n    },\r\n    // 根据sceneCode查找对应的sceneId\r\n    findSceneIdByCode(sceneCode) {\r\n      if (!sceneCode || !this.sceneTreeOptions) return ''\r\n      \r\n      const findInTree = (tree) => {\r\n        for (const node of tree) {\r\n          if (node.sceneCode === sceneCode) {\r\n            return node.id\r\n          }\r\n          if (node.children && node.children.length) {\r\n            const found = findInTree(node.children)\r\n            if (found) return found\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      \r\n      return findInTree(this.sceneTreeOptions) || ''\r\n    },\r\n    // 处理背景文件删除\r\n    handleRemoveBgFile(file, fileList) {\r\n      this.form.bgFileUrl = ''\r\n      this.form.bgImgUrl = '' // 同时清空背景图片首帧\r\n      this.bgFileList = []\r\n      this.$message.success('文件已删除')\r\n    },\r\n    // 更新背景文件列表\r\n    updateBgFileList() {\r\n      if (this.form.bgFileUrl) {\r\n        const fileName = this.form.bgFileUrl.split('/').pop()\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: this.form.bgFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.bgFileList = []\r\n      }\r\n    },\r\n    // 处理介绍视频文件删除\r\n    handleRemoveIntroduceVideoFile(file, fileList) {\r\n      this.introduceVideo.backgroundFileUrl = ''\r\n      this.introduceVideo.backgroundImgFileUrl = '' // 同时清空首帧图片\r\n      this.introduceVideoFileList = []\r\n      this.$message.success('介绍视频已删除')\r\n    },\r\n    // 更新介绍视频文件列表\r\n    updateIntroduceVideoFileList() {\r\n      if (this.introduceVideo.backgroundFileUrl) {\r\n        const fileName = this.introduceVideo.backgroundFileUrl.split('/').pop()\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: this.introduceVideo.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.introduceVideoFileList = []\r\n      }\r\n    },\r\n    // 处理讲解视频文件删除\r\n    handleRemoveVideoExplanationFile(file, fileList) {\r\n      this.videoExplanation.backgroundFileUrl = ''\r\n      this.videoExplanationFileList = []\r\n      this.$message.success('讲解视频已删除')\r\n    },\r\n    // 更新讲解视频文件列表\r\n    updateVideoExplanationFileList() {\r\n      if (this.videoExplanation.backgroundFileUrl) {\r\n        const fileName = this.videoExplanation.backgroundFileUrl.split('/').pop()\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: this.videoExplanation.backgroundFileUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.videoExplanationFileList = []\r\n      }\r\n    },\r\n    // 处理XML文件删除\r\n    handleRemoveXmlFile(file, fileList) {\r\n      this.form.panoramicViewXmlUrl = ''\r\n      this.xmlFileList = []\r\n      this.$message.success('XML文件已删除')\r\n    },\r\n    \r\n    // 更新XML文件列表\r\n    updateXmlFileList() {\r\n      if (this.form.panoramicViewXmlUrl) {\r\n        const fileName = this.form.panoramicViewXmlUrl.split('/').pop()\r\n        this.xmlFileList = [{\r\n          name: fileName,\r\n          url: this.form.panoramicViewXmlUrl,\r\n          uid: Date.now()\r\n        }]\r\n      } else {\r\n        this.xmlFileList = []\r\n      }\r\n    },\r\n    // 图片预览\r\n    previewImage(url) {\r\n      if (url) {\r\n        this.previewImageUrl = url\r\n        this.previewVisible = true\r\n      }\r\n    },\r\n    closePreview() {\r\n      this.previewVisible = false\r\n      this.previewImageUrl = ''\r\n    },\r\n    // 删除背景图片\r\n    deleteBgImage() {\r\n      this.$confirm('确定删除此图片吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.form.bgImgUrl = ''\r\n        this.$message.success('图片已删除')\r\n      }).catch(() => {})\r\n    },\r\n    async beforeUploadXmlFile(file) {\r\n      // 检查文件类型\r\n      if (!file.name.toLowerCase().endsWith('.xml')) {\r\n        this.$message.error('只能上传XML文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小（50MB）\r\n      if (file.size > 50 * 1024 * 1024) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        this.$modal.loading(\"正在上传XML文件，请稍候...\")\r\n        const formData = new FormData()\r\n        formData.append('file', file)\r\n        formData.append('industryCode', this.industryCode)\r\n        \r\n        const res = await uploadSceneFile(formData)\r\n        if (res.code === 0 && res.data) {\r\n          // 设置XML文件URL\r\n          this.form.panoramicViewXmlUrl = res.data.fileUrl\r\n          \r\n          // 直接覆盖XML文件列表\r\n          const fileName = res.data.fileUrl.split('/').pop()\r\n          this.xmlFileList = [{\r\n            name: fileName,\r\n            url: res.data.fileUrl,\r\n            uid: Date.now()\r\n          }]\r\n          \r\n          this.$message.success('上传成功')\r\n        } else {\r\n          this.$message.error(res.msg || '上传失败')\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('上传失败')\r\n      } finally {\r\n        this.$modal.closeLoading()\r\n      }\r\n      return false\r\n    },\r\n    // 主题变更回调\r\n    onThemeChange(theme) {\r\n      // 如果主题有默认背景图，可以自动设置\r\n      if (theme && theme.defaultBgImage) {\r\n        this.form.bgImgUrl = theme.defaultBgImage\r\n      }\r\n    },\r\n    // 格式化坐标数据用于提交\r\n    formatCoordinatesForSubmit(coordinates) {\r\n      if (!coordinates || !Array.isArray(coordinates)) {\r\n        return { clickX: '', clickY: '' }\r\n      }\r\n      \r\n      const xValues = coordinates.map(coord => coord.x || '0').join(',')\r\n      const yValues = coordinates.map(coord => coord.y || '0').join(',')\r\n      \r\n      return {\r\n        clickX: xValues,\r\n        clickY: yValues\r\n      }\r\n    },\r\n    // 解析坐标字符串为坐标数组\r\n    parseCoordinatesFromApi(clickX, clickY) {\r\n      const xArray = clickX ? clickX.split(',') : ['']\r\n      const yArray = clickY ? clickY.split(',') : ['']\r\n      \r\n      // 取较长的数组长度，确保坐标对齐\r\n      const maxLength = Math.max(xArray.length, yArray.length)\r\n      const coordinates = []\r\n      \r\n      for (let i = 0; i < maxLength; i++) {\r\n        coordinates.push({\r\n          x: xArray[i] || '',\r\n          y: yArray[i] || ''\r\n        })\r\n      }\r\n      \r\n      // 至少保证有一个坐标组\r\n      return coordinates.length > 0 ? coordinates : [{ x: '', y: '' }]\r\n    },\r\n    // 开始编辑标题\r\n    startEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = true\r\n      category.editingName = category.name\r\n      \r\n      // 下一帧聚焦输入框\r\n      this.$nextTick(() => {\r\n        // 使用动态ref名称\r\n        const inputRef = this.$refs[`titleInput_${index}`]\r\n        if (inputRef && inputRef[0]) {\r\n          inputRef[0].focus()\r\n          inputRef[0].select()\r\n        }\r\n      })\r\n    },\r\n\r\n    // 完成编辑标题\r\n    finishEditTitle(index) {\r\n      const category = this.categories[index]\r\n      if (category.editingName && category.editingName.trim()) {\r\n        category.name = category.editingName.trim()\r\n      }\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n\r\n    // 取消编辑标题\r\n    cancelEditTitle(index) {\r\n      const category = this.categories[index]\r\n      category.editing = false\r\n      category.editingName = ''\r\n    },\r\n    // 设置上传模式\r\n    setUploadMode(type, mode) {\r\n      this.$set(this.uploadModes, type, mode)\r\n    },\r\n    // 背景文件链接输入处理\r\n    handleBgFileUrlInput(value) {\r\n      this.bgFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.bgFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 视频讲解链接输入处理\r\n    handleVideoExplanationUrlInput(value) {\r\n      this.videoExplanationFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.videoExplanationFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 介绍视频链接输入处理\r\n    handleIntroduceVideoUrlInput(value) {\r\n      this.introduceVideoFileList = []\r\n      if (value) {\r\n        const fileName = value.split('/').pop() || '外部链接文件'\r\n        this.introduceVideoFileList = [{\r\n          name: fileName,\r\n          url: value,\r\n          uid: Date.now()\r\n        }]\r\n      }\r\n    },\r\n    // 同步文件\r\n    async handleSynchronizeFile() {\r\n      if (!this.form.sceneViewConfigId) {\r\n        this.$message.warning('请先保存配置后再同步文件')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        this.synchronizing = true\r\n        this.$modal.loading(\"正在同步文件，请稍候...\")\r\n        \r\n        // 使用FormData或URLSearchParams传递表单参数\r\n        const formData = new FormData()\r\n        formData.append('viewConfigId', this.form.sceneViewConfigId)\r\n        \r\n        const res = await synchronizationFile(formData)\r\n        \r\n        if (res.code === 0) {\r\n          this.$message.success(res.msg)\r\n        } else {\r\n          this.$message.error(res.msg || '文件同步失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('同步文件失败:', error)\r\n        this.$message.error('文件同步失败')\r\n      } finally {\r\n        this.synchronizing = false\r\n        this.$modal.closeLoading()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-container {\r\n  display: flex;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n}\r\n\r\n.menu-panel {\r\n  width: 250px;\r\n  background-color: #f5f7fa;\r\n  border-right: 1px solid #e4e7ed;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n  background-color: #f5f7fa;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.menu-tree::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-track {\r\n  background-color: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb {\r\n  background-color: #c0c0c0;\r\n  border-radius: 3px;\r\n}\r\n\r\n.menu-tree::-webkit-scrollbar-thumb:hover {\r\n  background-color: #a0a0a0;\r\n}\r\n\r\n.content-panel {\r\n  flex: 1;\r\n  padding: 20px 20px 80px 20px;\r\n  overflow-y: auto;\r\n  background-color: #fff;\r\n  position: relative;\r\n}\r\n\r\n/* 在切换行业loading期间禁止滚动 */\r\n.content-panel.loading-no-scroll {\r\n  overflow: hidden;\r\n}\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.category-block {\r\n  margin-top: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.category-title:hover {\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.category-title span {\r\n  display: inline-block;\r\n  min-width: 100px;\r\n}\r\n\r\n.category-body {\r\n  padding: 12px;\r\n  background: #ffffff;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.sub-category-block {\r\n  margin-bottom: 15px;\r\n}\r\n.sub-category-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.sub-category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px;\r\n  background-color: #fafafa;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.sub-category-title {\r\n  font-weight: 500;\r\n}\r\n\r\n.sub-category-body {\r\n  padding: 15px;\r\n}\r\n\r\n.segment-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pain-point-block {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.pain-point-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.mini-block {\r\n  margin-bottom: 20px;\r\n  min-height: 450px; /* 设置统一的最小高度 */\r\n}\r\n\r\n.mini-block:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* 确保卡片内容区域也有合适的高度 */\r\n.mini-block .el-card__body {\r\n  min-height: 450px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 视频卡片保持原有样式 */\r\n.video-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 450px;\r\n}\r\n\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 450px;\r\n}\r\n.video-card-yu{\r\n  min-height: 300px;\r\n}\r\n.video-card .el-card__body {\r\n  flex: 1;\r\n  overflow: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.segment-scroll {\r\n  max-height: 150px;\r\n  overflow-y: auto;\r\n  border: 1px solid #eee;\r\n  border-radius: 4px;\r\n  padding: 8px;\r\n  background: #fafbfc;\r\n}\r\n.scene-config-container {\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 限制上传图片的显示大小 */\r\n.image-upload .el-upload--picture-card {\r\n  width: 148px;\r\n  height: 148px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 介绍视频首帧图片大小控制 */\r\n.image-upload .el-upload-list__item-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n/* 上传框也添加圆角 */\r\n.image-upload .el-upload--picture-card {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 图片预览样式 */\r\n.preview-container {\r\n  text-align: center;\r\n}\r\n\r\n.preview-image {\r\n  max-width: 100%;\r\n  max-height: 70vh;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 图片悬停操作样式 */\r\n.image-preview-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n  border-radius: 6px;\r\n}\r\n\r\n.image-preview-container:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.preview-icon,\r\n.delete-icon {\r\n  color: white;\r\n  font-size: 20px;\r\n  margin: 0 10px;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.preview-icon:hover,\r\n.delete-icon:hover {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.submit-footer {\r\n  position: fixed;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 250px;\r\n  height: 60px;\r\n  background: #fff;\r\n  border-top: 1px solid #e4e7ed;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node__content:hover {\r\n  background-color: #e6f7ff;\r\n}\r\n\r\n.menu-tree ::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.custom-tree-node {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.highlight {\r\n  background-color: yellow;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n  justify-content: flex-end;\r\n  padding: 0 20px;\r\n}\r\n</style>\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n}\r\n\r\n.submit-footer .el-button {\r\n  min-width: 100px;\r\n}\r\n\r\n.menu-search {\r\n  padding: 16px;\r\n  border-bottom: 1px solid #e6e6e6;\r\n}\r\n\r\n.menu-search .el-input {\r\n  border-radius: 20px;\r\n}\r\n\r\n.menu-search .el-input__inner {\r\n  border-radius: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\n.highlight {\r\n  background-color: #ffeb3b;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.menu-tree {\r\n  background\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0VA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,kBAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,oBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,qBAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,MAAA,GAAAN,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAS,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,mBAAA,EAAAA,4BAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,oBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,QAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MAAA;MACAC,IAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,mBAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,OAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,KAAA;QACAC,oBAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,qBAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,uBAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,aAAA;MACAC,YAAA;MACAC,UAAA;MAAA;MACAC,cAAA;QACAC,MAAA;QACAC,oBAAA;QACAC,iBAAA;MACA;MACAC,gBAAA;QACAH,MAAA;QACAE,iBAAA;QACAE,oBAAA;MACA;MACAC,gBAAA;MACAC,kBAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,aAAA;QACAC,QAAA,WAAAA,SAAAxC,IAAA;UACA;UACA,IAAAyC,UAAA,GAAAxC,KAAA,CAAA8B,gBAAA,IAAA9B,KAAA,CAAA8B,gBAAA,CAAAC,oBAAA,GACA/B,KAAA,CAAA8B,gBAAA,CAAAC,oBAAA,CAAAU,IAAA,WAAAC,GAAA;YAAA,OAAAA,GAAA,CAAAC,OAAA,KAAA5C,IAAA,CAAA6C,EAAA;UAAA,KACA;UACA,OAAAJ,UAAA;QACA;MACA;MACAK,UAAA;MAAA;MACAC,wBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,cAAA;MAAA;MACA;MACAC,cAAA;MACAC,eAAA;MACAC,aAAA;MACAC,gBAAA;MAAA;MACAC,WAAA;QACAC,MAAA;QACA1B,gBAAA;QACAJ,cAAA;MACA;MACA+B,aAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA;MACA,UAAA9B,gBAAA,CAAAC,oBAAA,SAAAD,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA;QACA;UAAAC,IAAA;UAAAnB,OAAA;UAAAoB,SAAA;UAAAC,SAAA;QAAA;MACA;MACA,YAAAlC,gBAAA,CAAAC,oBAAA;IACA;IACAkC,eAAA;MACAC,GAAA,WAAAA,IAAA;QACA,IAAAjE,QAAA,QAAA+C,kBAAA,MAAA7C,UAAA;QACA,KAAAF,QAAA;UACA;YACAkE,gBAAA;YACAC,kBAAA;cACAzC,MAAA;cACAE,iBAAA;cACAE,oBAAA;YACA;UACA;QACA;QACA,OAAA9B,QAAA;MACA;MACAoE,GAAA,WAAAA,IAAAlC,KAAA;QACA,KAAAmC,IAAA,MAAAtB,kBAAA,OAAA7C,UAAA,EAAAgC,KAAA;MACA;IACA;IACAoC,iBAAA;MACAL,GAAA,WAAAA,IAAA;QACA,YAAAjB,oBAAA,MAAA9C,UAAA;MACA;MACAkE,GAAA,WAAAA,IAAAlC,KAAA;QACA,KAAAmC,IAAA,MAAArB,oBAAA,OAAA9C,UAAA,EAAAgC,KAAA;MACA;IACA;IACAqC,WAAA;MACAN,GAAA,WAAAA,IAAA;QACA,YAAAhB,cAAA,MAAA/C,UAAA;MACA;MACAkE,GAAA,WAAAA,IAAAI,GAAA;QACA,KAAAH,IAAA,MAAApB,cAAA,OAAA/C,UAAA,EAAAsE,GAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,UAAAtB,aAAA;QACA,YAAApD,QAAA;MACA;;MAEA;MACA,IAAA2E,UAAA,YAAAA,WAAAC,KAAA;QACA,OAAAA,KAAA,CAAAC,GAAA,WAAAC,IAAA;UACA,IAAAC,gBAAA,GAAAD,IAAA,CAAA3C,QAAA,GAAAwC,UAAA,CAAAG,IAAA,CAAA3C,QAAA;UACA,IAAA6C,aAAA,GAAAF,IAAA,CAAAvF,IAAA,IAAAuF,IAAA,CAAAvF,IAAA,CAAA0F,WAAA,GAAAC,QAAA,CAAAR,MAAA,CAAAtB,aAAA,CAAA6B,WAAA;UAEA,IAAAD,aAAA,IAAAD,gBAAA,CAAAnB,MAAA;YACA,WAAAuB,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAN,IAAA;cACA3C,QAAA,EAAA4C;YAAA;UAEA;UACA;QACA,GAAAM,MAAA,CAAAC,OAAA;MACA;MAEA,OAAAX,UAAA,MAAA3E,QAAA;IACA;EACA;EACAuF,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,gBAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA;IACAF,gBAAA,WAAAA,iBAAA;MACA,IAAAG,SAAA,OAAAC,eAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;MACA,IAAAC,KAAA,GAAAL,SAAA,CAAA1B,GAAA;MAEA,IAAA+B,KAAA;QACAC,YAAA,CAAAC,OAAA,mBAAAF,KAAA;QACAG,cAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,MAAA,8BAAAC,MAAA,CAAAP,KAAA;QACAQ,OAAA,CAAAC,GAAA,kBAAAT,KAAA;MACA;QACA,IAAAU,WAAA,GAAAT,YAAA,CAAAU,OAAA;QACA,IAAAD,WAAA;UACAP,cAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,MAAA,8BAAAC,MAAA,CAAAG,WAAA;QACA;MACA;IACA;IACAjB,gBAAA,WAAAA,iBAAA;MAAA,IAAAmB,MAAA;MAAA,WAAAC,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,yBAAA;YAAA;cAAAN,GAAA,GAAAG,QAAA,CAAAI,IAAA;cAAA,MACAP,GAAA,CAAAQ,IAAA,UAAAC,KAAA,CAAAC,OAAA,CAAAV,GAAA,CAAAnH,IAAA;gBAAAsH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA;cACAV,MAAA,CAAA5G,QAAA,GAAAiH,GAAA,CAAAnH,IAAA,CAAA+E,GAAA,WAAA+C,KAAA;gBAAA;kBACAjF,EAAA,WAAA4D,MAAA,CAAAqB,KAAA,CAAAC,QAAA;kBACAtI,IAAA,EAAAqI,KAAA,CAAAE,SAAA;kBACAC,IAAA;kBACA5F,QAAA,EAAAyF,KAAA,CAAAI,mBAAA,GAAAJ,KAAA,CAAAI,mBAAA,CAAAnD,GAAA,WAAAoD,QAAA;oBAAA;sBACAtF,EAAA,EAAAsF,QAAA,CAAAtF,EAAA;sBACApD,IAAA,EAAA0I,QAAA,CAAAC,YAAA;sBACA/H,YAAA,EAAA8H,QAAA,CAAA9H,YAAA;sBACAyH,KAAA,EAAAK,QAAA,CAAAL,KAAA;sBACAG,IAAA;oBACA;kBAAA;gBACA;cAAA;;cAEA;cACAnB,MAAA,CAAA3G,YAAA;cACAgH,GAAA,CAAAnH,IAAA,CAAAqI,OAAA,WAAAP,KAAA;gBACA,IAAAA,KAAA,CAAAI,mBAAA;kBAAA,IAAAI,mBAAA;kBACA,CAAAA,mBAAA,GAAAxB,MAAA,CAAA3G,YAAA,EAAAoI,IAAA,CAAAC,KAAA,CAAAF,mBAAA,MAAAG,mBAAA,CAAAnD,OAAA,EAAAwC,KAAA,CAAAI,mBAAA;gBACA;cACA;;cAEA;cAAA,KACApB,MAAA,CAAA3G,YAAA,CAAA2D,MAAA;gBAAAwD,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAV,MAAA,CAAA1G,UAAA,GAAAsI,MAAA,CAAA5B,MAAA,CAAA3G,YAAA,IAAA0C,EAAA;cACAiE,MAAA,CAAAzG,YAAA,GAAAyG,MAAA,CAAA3G,YAAA,IAAAE,YAAA;cACA;cACAyG,MAAA,CAAA6B,SAAA;gBACA;gBACA,IAAA7B,MAAA,CAAA8B,KAAA,CAAAC,QAAA,IAAA/B,MAAA,CAAA8B,KAAA,CAAAC,QAAA,CAAAC,aAAA;kBACAhC,MAAA,CAAA8B,KAAA,CAAAC,QAAA,CAAAC,aAAA,CAAAhC,MAAA,CAAA1G,UAAA;gBACA;cACA;;cAEA;cAAAkH,QAAA,CAAAE,IAAA;cAAA,OACAV,MAAA,CAAAiC,YAAA,CAAAjC,MAAA,CAAA1G,UAAA;YAAA;YAAA;cAAA,OAAAkH,QAAA,CAAA0B,IAAA;UAAA;QAAA,GAAA9B,OAAA;MAAA;IAGA;IAEA+B,mBAAA,WAAAA,oBAAAjJ,IAAA;MACA;MACA,IAAAA,IAAA,CAAAiI,IAAA;QACA,KAAAc,YAAA,CAAAL,MAAA,CAAA1I,IAAA,CAAA6C,EAAA;QACA,KAAAxC,YAAA,GAAAL,IAAA,CAAAK,YAAA;MACA;IACA;IAEA6I,YAAA,WAAAA,aAAA9G,KAAA;MACA,KAAAkB,aAAA,GAAAlB,KAAA;IACA;IACA+G,aAAA,WAAAA,cAAAC,IAAA;MACA,UAAA9F,aAAA,SAAA8F,IAAA;MACA,IAAAC,KAAA,OAAAC,MAAA,KAAA7C,MAAA,MAAAnD,aAAA;MACA,OAAA8F,IAAA,CAAAG,OAAA,CAAAF,KAAA;IACA;IACAN,YAAA,WAAAA,aAAAlG,EAAA;MAAA,IAAA2G,UAAA,GAAAC,SAAA;QAAAC,MAAA;MAAA,WAAA3C,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA0C,SAAA;QAAA,IAAAC,gBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,eAAA,EAAA1J,YAAA,EAAA8G,GAAA,EAAA6C,aAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,WAAA;QAAA,WAAAnD,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAgD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAA7C,IAAA;YAAA;cAAAoC,gBAAA,GAAAJ,UAAA,CAAA1F,MAAA,QAAA0F,UAAA,QAAAc,SAAA,GAAAd,UAAA;cAAAK,WAAA,GAAAL,UAAA,CAAA1F,MAAA,QAAA0F,UAAA,QAAAc,SAAA,GAAAd,UAAA;cAAAa,SAAA,CAAA9C,IAAA;cAEA;cACA,IAAAsC,WAAA;gBACAH,MAAA,CAAA1I,iBAAA;gBACA;gBACAuJ,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;;gBAEA;gBACAhB,MAAA,CAAAf,SAAA;kBACA,IAAAgC,YAAA,GAAAJ,QAAA,CAAAK,aAAA;kBACA,IAAAD,YAAA;oBACAA,YAAA,CAAAE,SAAA;kBACA;gBACA;cACA;cAEAnB,MAAA,CAAAtJ,UAAA,GAAAyC,EAAA;cAAAwH,SAAA,CAAA7C,IAAA;cAAA,OACAkC,MAAA,CAAAoB,oBAAA,CAAApB,MAAA,CAAAtJ,UAAA;YAAA;cAEA;cACA0J,mBAAA,GAAAF,gBAAA,GAAAF,MAAA,CAAA5I,YAAA,SAEA;cACA4I,MAAA,CAAApJ,aAAA;;cAEA;cACAyJ,eAAA,GAAAL,MAAA,CAAAvJ,YAAA,CAAA4K,IAAA,WAAAC,IAAA;gBAAA,OAAAtC,MAAA,CAAAsC,IAAA,CAAAnI,EAAA,MAAAA,EAAA;cAAA;cACAxC,YAAA,GAAA0J,eAAA,GAAAA,eAAA,CAAA1J,YAAA;cAAAgK,SAAA,CAAA7C,IAAA;cAAA,OAEA,IAAAyD,6BAAA;gBAAA5K,YAAA,EAAAA;cAAA;YAAA;cAAA8G,GAAA,GAAAkD,SAAA,CAAA3C,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBAEA;gBACA0J,MAAA,CAAAnJ,IAAA,CAAA2K,iBAAA,GAAA/D,GAAA,CAAAnH,IAAA,CAAAkL,iBAAA;gBACAxB,MAAA,CAAAnJ,IAAA,CAAAC,SAAA,GAAA2G,GAAA,CAAAnH,IAAA,CAAAQ,SAAA;gBACAkJ,MAAA,CAAAnJ,IAAA,CAAAE,QAAA,GAAA0G,GAAA,CAAAnH,IAAA,CAAAS,QAAA;gBACAiJ,MAAA,CAAAnJ,IAAA,CAAAG,QAAA,GAAAyG,GAAA,CAAAnH,IAAA,CAAA6B,oBAAA;gBACA6H,MAAA,CAAAnJ,IAAA,CAAAI,SAAA,GAAAwG,GAAA,CAAAnH,IAAA,CAAA8B,iBAAA;gBACA4H,MAAA,CAAAnJ,IAAA,CAAAK,mBAAA,GAAAuG,GAAA,CAAAnH,IAAA,CAAAY,mBAAA;;gBAEA;gBACA8I,MAAA,CAAAyB,gBAAA;;gBAEA;gBACAzB,MAAA,CAAA0B,iBAAA;;gBAEA;gBACA,IAAAjE,GAAA,CAAAnH,IAAA,CAAAqL,WAAA;kBACA3B,MAAA,CAAApJ,aAAA;oBACAgL,OAAA,EAAAnE,GAAA,CAAAnH,IAAA,CAAAqL,WAAA,CAAAC,OAAA;oBACAC,SAAA,EAAApE,GAAA,CAAAnH,IAAA,CAAAqL,WAAA,CAAAE,SAAA;oBACAC,cAAA,EAAArE,GAAA,CAAAnH,IAAA,CAAAqL,WAAA,CAAAG,cAAA;oBACAC,MAAA,EAAAtE,GAAA,CAAAnH,IAAA,CAAAqL,WAAA,CAAAI;kBACA;gBACA;kBACA/B,MAAA,CAAApJ,aAAA;gBACA;;gBAEA;gBACA,IAAA6G,GAAA,CAAAnH,IAAA,CAAA0L,wBAAA,IAAA9D,KAAA,CAAAC,OAAA,CAAAV,GAAA,CAAAnH,IAAA,CAAA0L,wBAAA;kBACAhC,MAAA,CAAAhI,UAAA,GAAAyF,GAAA,CAAAnH,IAAA,CAAA0L,wBAAA,CAAA3G,GAAA,WAAA4G,UAAA;oBAAA;sBACA9I,EAAA,EAAA8I,UAAA,CAAA9I,EAAA;sBACA+I,GAAA,EAAAD,UAAA,CAAAE,OAAA;sBACApM,IAAA,EAAAkM,UAAA,CAAAlM,IAAA;sBACAqM,OAAA,EAAAH,UAAA,CAAAI,QAAA;sBAAA;sBACAC,OAAA;sBACAC,WAAA;sBACAC,YAAA,EAAAP,UAAA,CAAAlM,IAAA;sBACAgM,MAAA,EAAAE,UAAA,CAAAF,MAAA;sBACAU,cAAA,EAAAR,UAAA,CAAAQ,cAAA;sBACAC,aAAA,EAAAT,UAAA,CAAAS;oBACA;kBAAA;;kBAEA;kBACApC,aAAA,GAAA7C,GAAA,CAAAnH,IAAA,CAAA0L,wBAAA,CAAAX,IAAA,WAAAC,IAAA;oBAAA,OAAAA,IAAA,CAAAa,OAAA;kBAAA,IAEA;kBACA,IAAA7B,aAAA,IAAAA,aAAA,CAAAqC,mBAAA,IAAArC,aAAA,CAAAqC,mBAAA,CAAAhI,kBAAA;oBACA4F,SAAA,GAAAD,aAAA,CAAAqC,mBAAA,CAAAhI,kBAAA;oBACAqF,MAAA,CAAA3H,gBAAA;sBACAH,MAAA,EAAAqI,SAAA,CAAArI,MAAA;sBACAE,iBAAA,EAAAmI,SAAA,CAAAnI,iBAAA;sBACAE,oBAAA,EAAAiI,SAAA,CAAAjI,oBAAA,GAAAiI,SAAA,CAAAjI,oBAAA,CAAA+C,GAAA,WAAApC,GAAA;wBAAA;0BACAoB,IAAA,EAAApB,GAAA,CAAAoB,IAAA;0BACAE,SAAA,EAAAtB,GAAA,CAAAsB,SAAA;0BACAD,SAAA,EAAArB,GAAA,CAAAqB,SAAA;0BACApB,OAAA,EAAA8G,MAAA,CAAA4C,iBAAA,CAAA3J,GAAA,CAAAsB,SAAA;wBACA;sBAAA;oBACA;kBACA;oBACAyF,MAAA,CAAA3H,gBAAA;sBACAH,MAAA;sBACAE,iBAAA;sBACAE,oBAAA;oBACA;kBACA;;kBAEA;kBACA0H,MAAA,CAAA6C,8BAAA;;kBAEA;kBACA,IAAAvC,aAAA,IAAAA,aAAA,CAAAqC,mBAAA,IAAArC,aAAA,CAAAqC,mBAAA,CAAAG,WAAA;oBACA9C,MAAA,CAAA7I,eAAA,GAAA6I,MAAA,CAAA+C,cAAA,CAAAzC,aAAA,CAAAqC,mBAAA,CAAAG,WAAA;;oBAEA;oBACA,IAAA5C,gBAAA,IAAAE,mBAAA;sBACAI,YAAA,GAAAR,MAAA,CAAAgD,YAAA,CAAAhD,MAAA,CAAA7I,eAAA,EAAAiJ,mBAAA,CAAAjH,EAAA;sBACA,IAAAqH,YAAA;wBACAR,MAAA,CAAA5I,YAAA,GAAAoJ,YAAA;sBACA;wBACAR,MAAA,CAAA5I,YAAA,GAAA4I,MAAA,CAAA7I,eAAA,CAAAiD,MAAA,OAAA4F,MAAA,CAAA7I,eAAA;sBACA;oBACA;sBACA;sBACA6I,MAAA,CAAA5I,YAAA,GAAA4I,MAAA,CAAA7I,eAAA,CAAAiD,MAAA,OAAA4F,MAAA,CAAA7I,eAAA;oBACA;kBACA;oBACA;oBACA6I,MAAA,CAAA7I,eAAA;oBACA6I,MAAA,CAAA5I,YAAA;kBACA;gBACA;;gBAEA;gBACA,IAAAqG,GAAA,CAAAnH,IAAA,CAAA2M,iBAAA;kBACAxC,WAAA;oBACA/F,gBAAA,EAAA+C,GAAA,CAAAnH,IAAA,CAAA2M,iBAAA,CAAAvI,gBAAA;oBACAC,kBAAA,EAAA8C,GAAA,CAAAnH,IAAA,CAAA2M,iBAAA,CAAAtI,kBAAA;sBACAzC,MAAA;sBACAE,iBAAA;sBACAE,oBAAA;oBACA;kBACA;kBACA0H,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAzG,kBAAA,EAAAyG,MAAA,CAAAtJ,UAAA,EAAA+J,WAAA;gBACA;;gBAEA;gBACA,IAAAhD,GAAA,CAAAnH,IAAA,CAAA4M,qBAAA;kBACAlD,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAxG,oBAAA,EAAAwG,MAAA,CAAAtJ,UAAA,EAAA+G,GAAA,CAAAnH,IAAA,CAAA4M,qBAAA;gBACA;;gBAEA;gBACA,IAAAzF,GAAA,CAAAnH,IAAA,CAAA6M,YAAA;kBACAnD,MAAA,CAAAnF,IAAA,CAAAmF,MAAA,CAAAvG,cAAA,EAAAuG,MAAA,CAAAtJ,UAAA,EAAA+G,GAAA,CAAAnH,IAAA,CAAA6M,YAAA;gBACA;;gBAEA;cACA;cAAAxC,SAAA,CAAA7C,IAAA;cAAA;YAAA;cAAA6C,SAAA,CAAA9C,IAAA;cAAA8C,SAAA,CAAAyC,EAAA,GAAAzC,SAAA;cAEA3D,OAAA,CAAAqG,KAAA,YAAA1C,SAAA,CAAAyC,EAAA;cACApD,MAAA,CAAAsD,QAAA,CAAAD,KAAA;YAAA;cAAA1C,SAAA,CAAA9C,IAAA;cAEA;cACA,IAAAsC,WAAA;gBACAH,MAAA,CAAA1I,iBAAA;gBACA;gBACAuJ,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;cACA;cAAA,OAAAL,SAAA,CAAA4C,MAAA;YAAA;YAAA;cAAA,OAAA5C,SAAA,CAAArB,IAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IAEA;IACAuD,kBAAA,WAAAA,mBAAAC,IAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAA7M,IAAA,CAAAyB,oBAAA,CAAAuG,IAAA;QAAAxE,IAAA;QAAAsJ,KAAA;MAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,SAAAhN,IAAA,CAAAyB,oBAAA,CAAA8B,MAAA;QACA,KAAAvD,IAAA,CAAAyB,oBAAA,CAAAwL,MAAA,CAAAD,KAAA;MACA;IACA;IACAE,wBAAA,WAAAA,yBAAAN,IAAA,EAAAlF,IAAA,EAAA2D,GAAA;MAAA,IAAA8B,MAAA;MAAA,WAAA3G,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA0G,SAAA;QAAA,IAAAC,QAAA,EAAAzG,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAyG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvG,IAAA,GAAAuG,SAAA,CAAAtG,IAAA;YAAA;cAAA,IACA2F,IAAA,CAAAlF,IAAA,CAAA8F,UAAA;gBAAAD,SAAA,CAAAtG,IAAA;gBAAA;cAAA;cACAkG,MAAA,CAAAV,QAAA,CAAAD,KAAA;cAAA,OAAAe,SAAA,CAAAE,MAAA,WACA;YAAA;cAAAF,SAAA,CAAAvG,IAAA;cAIAmG,MAAA,CAAAO,MAAA,CAAAlN,OAAA;cACA6M,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,QAAA,CAAAO,MAAA,iBAAAT,MAAA,CAAArN,YAAA;cAAAyN,SAAA,CAAAtG,IAAA;cAAA,OAEA,IAAA4G,0BAAA,EAAAR,QAAA;YAAA;cAAAzG,GAAA,GAAA2G,SAAA,CAAApG,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBACA,IAAAiI,IAAA,IAAA2D,GAAA;kBACA;kBACA8B,MAAA,CAAAzF,IAAA,EAAA2D,GAAA,IAAAzE,GAAA,CAAAnH,IAAA,CAAAqO,OAAA;gBACA;kBACA;kBACAX,MAAA,CAAAnN,IAAA,CAAAG,QAAA,GAAAyG,GAAA,CAAAnH,IAAA,CAAAqO,OAAA;gBACA;gBACAX,MAAA,CAAAV,QAAA,CAAAsB,OAAA;cACA;gBACAZ,MAAA,CAAAV,QAAA,CAAAD,KAAA,CAAA5F,GAAA,CAAAoH,GAAA;cACA;cAAAT,SAAA,CAAAtG,IAAA;cAAA;YAAA;cAAAsG,SAAA,CAAAvG,IAAA;cAAAuG,SAAA,CAAAhB,EAAA,GAAAgB,SAAA;cAEAJ,MAAA,CAAAV,QAAA,CAAAD,KAAA;YAAA;cAAAe,SAAA,CAAAvG,IAAA;cAEAmG,MAAA,CAAAO,MAAA,CAAAO,YAAA;cAAA,OAAAV,SAAA,CAAAb,MAAA;YAAA;cAAA,OAAAa,SAAA,CAAAE,MAAA,WAEA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAA9E,IAAA;UAAA;QAAA,GAAA2E,QAAA;MAAA;IACA;IACAc,0BAAA,WAAAA,2BAAAtB,IAAA,EAAAlF,IAAA,EAAA2D,GAAA;MAAA,IAAA8C,MAAA;MAAA,WAAA3H,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA0H,SAAA;QAAA,IAAAf,QAAA,EAAAzG,GAAA,EAAAyH,QAAA,EAAAC,SAAA,EAAAC,IAAA,EAAAC,SAAA;QAAA,WAAA/H,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAA4H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,IAAA,GAAA0H,SAAA,CAAAzH,IAAA;YAAA;cAAA,MAEA,CAAAS,IAAA,KAAA2D,GAAA;gBAAAqD,SAAA,CAAAzH,IAAA;gBAAA;cAAA;cAAAyH,SAAA,CAAA1H,IAAA;cAEAmH,MAAA,CAAAT,MAAA,CAAAlN,OAAA;cACA6M,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,QAAA,CAAAO,MAAA,iBAAAO,MAAA,CAAArO,YAAA;cAAA4O,SAAA,CAAAzH,IAAA;cAAA,OAEA,IAAA4G,0BAAA,EAAAR,QAAA;YAAA;cAAAzG,GAAA,GAAA8H,SAAA,CAAAvH,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBACA;gBACA0O,MAAA,CAAAnO,IAAA,CAAAI,SAAA,GAAAwG,GAAA,CAAAnH,IAAA,CAAAqO,OAAA;;gBAEA;gBACAO,QAAA,GAAAzH,GAAA,CAAAnH,IAAA,CAAAqO,OAAA,CAAAa,KAAA,MAAAC,GAAA;gBACAT,MAAA,CAAA5L,UAAA;kBACArD,IAAA,EAAAmP,QAAA;kBACAQ,GAAA,EAAAjI,GAAA,CAAAnH,IAAA,CAAAqO,OAAA;kBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;gBACA;;gBAEA;gBACA,IAAApC,IAAA,CAAAlF,IAAA,oBAAAd,GAAA,CAAAnH,IAAA,CAAAwP,MAAA;kBACAd,MAAA,CAAAnO,IAAA,CAAAG,QAAA,GAAAyG,GAAA,CAAAnH,IAAA,CAAAwP,MAAA;kBACAd,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;gBACA;kBACAI,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;gBACA;cACA;gBACAI,MAAA,CAAA1B,QAAA,CAAAD,KAAA,CAAA5F,GAAA,CAAAoH,GAAA;cACA;cAAAU,SAAA,CAAAzH,IAAA;cAAA;YAAA;cAAAyH,SAAA,CAAA1H,IAAA;cAAA0H,SAAA,CAAAnC,EAAA,GAAAmC,SAAA;cAEAP,MAAA,CAAA1B,QAAA,CAAAD,KAAA;YAAA;cAAAkC,SAAA,CAAA1H,IAAA;cAEAmH,MAAA,CAAAT,MAAA,CAAAO,YAAA;cAAA,OAAAS,SAAA,CAAAhC,MAAA;YAAA;cAAA,OAAAgC,SAAA,CAAAjB,MAAA,WAEA;YAAA;cAAA,MAIA,CAAAb,IAAA,CAAAlF,IAAA,CAAA8F,UAAA,eAAAZ,IAAA,CAAA1N,IAAA,CAAAgQ,QAAA;gBAAAR,SAAA,CAAAzH,IAAA;gBAAA;cAAA;cACAkH,MAAA,CAAA1B,QAAA,CAAAD,KAAA;cAAA,OAAAkC,SAAA,CAAAjB,MAAA,WACA;YAAA;cAAAiB,SAAA,CAAA1H,IAAA;cAIAmH,MAAA,CAAAT,MAAA,CAAAlN,OAAA;cACA6M,SAAA,OAAAM,QAAA;cACAN,SAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,SAAA,CAAAO,MAAA,iBAAAO,MAAA,CAAArO,YAAA;cAAA4O,SAAA,CAAAzH,IAAA;cAAA,OAEA,IAAA4G,0BAAA,EAAAR,SAAA;YAAA;cAAAzG,IAAA,GAAA8H,SAAA,CAAAvH,IAAA;cACA,IAAAP,IAAA,CAAAQ,IAAA,UAAAR,IAAA,CAAAnH,IAAA;gBACA,IAAAiI,IAAA,IAAA2D,GAAA;kBACA;kBACA8C,MAAA,CAAAzG,IAAA,EAAA2D,GAAA,IAAAzE,IAAA,CAAAnH,IAAA,CAAAqO,OAAA;;kBAEA;kBACAO,SAAA,GAAAzH,IAAA,CAAAnH,IAAA,CAAAqO,OAAA,CAAAa,KAAA,MAAAC,GAAA;kBACA,IAAAlH,IAAA,yBAAA2D,GAAA;oBACA8C,MAAA,CAAAgB,sBAAA;sBACAjQ,IAAA,EAAAmP,SAAA;sBACAQ,GAAA,EAAAjI,IAAA,CAAAnH,IAAA,CAAAqO,OAAA;sBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;oBACA;kBACA,WAAAtH,IAAA,2BAAA2D,GAAA;oBACA8C,MAAA,CAAA3L,wBAAA;sBACAtD,IAAA,EAAAmP,SAAA;sBACAQ,GAAA,EAAAjI,IAAA,CAAAnH,IAAA,CAAAqO,OAAA;sBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAtH,IAAA,yBAAA2D,GAAA,4BAAAzE,IAAA,CAAAnH,IAAA,CAAAwP,MAAA;oBACAd,MAAA,CAAA/M,cAAA,CAAAE,oBAAA,GAAAsF,IAAA,CAAAnH,IAAA,CAAAwP,MAAA;oBACAd,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;kBACA;oBACAI,MAAA,CAAA1B,QAAA,CAAAsB,OAAA;kBACA;gBACA;cACA;gBACAI,MAAA,CAAA1B,QAAA,CAAAD,KAAA,CAAA5F,IAAA,CAAAoH,GAAA;cACA;cAAAU,SAAA,CAAAzH,IAAA;cAAA;YAAA;cAAAyH,SAAA,CAAA1H,IAAA;cAAA0H,SAAA,CAAAU,EAAA,GAAAV,SAAA;cAEAP,MAAA,CAAA1B,QAAA,CAAAD,KAAA;YAAA;cAAAkC,SAAA,CAAA1H,IAAA;cAEAmH,MAAA,CAAAT,MAAA,CAAAO,YAAA;cAAA,OAAAS,SAAA,CAAAhC,MAAA;YAAA;cAAA,OAAAgC,SAAA,CAAAjB,MAAA,WAEA;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAAjG,IAAA;UAAA;QAAA,GAAA2F,QAAA;MAAA;IACA;IACAiB,4BAAA,WAAAA,6BAAAzC,IAAA;MACA,KAAA3L,aAAA;MACA,KAAAC,YAAA;MACA,YAAAyL,kBAAA,CAAAC,IAAA;IACA;IACA;IACA0C,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,QAAA,GAAArG,SAAA,CAAA3F,MAAA,QAAA2F,SAAA,QAAAa,SAAA,GAAAb,SAAA;MACA,IAAAsG,OAAA;QACAlN,EAAA,EAAAyM,IAAA,CAAAC,GAAA;QAAA;QACA9P,IAAA;QACAwI,IAAA;QAAA;QACA6D,OAAA;QACAzJ,QAAA;QACAyN,QAAA,EAAAA;MACA;MACA,IAAAA,QAAA;QACA,IAAAE,UAAA,QAAAtD,YAAA,MAAA7L,eAAA,EAAAiP,QAAA;QACA,IAAAE,UAAA;UACAA,UAAA,CAAA3N,QAAA,CAAAkG,IAAA,CAAAwH,OAAA;QACA;MACA;QACA,KAAAlP,eAAA,CAAA0H,IAAA,CAAAwH,OAAA;MACA;MACA,OAAAA,OAAA,CAAAlN,EAAA;IACA;IACA;IACAoN,qBAAA,WAAAA,sBAAAC,MAAA;MACA,KAAArP,eAAA,QAAAA,eAAA,CAAA0E,MAAA,WAAAP,IAAA;QAAA,OAAAA,IAAA,CAAAnC,EAAA,KAAAqN,MAAA;MAAA;IACA;IACA;IACAxD,YAAA,WAAAA,aAAA5H,KAAA,EAAAjC,EAAA;MAAA,IAAAsN,SAAA,OAAAC,2BAAA,CAAA9K,OAAA,EACAR,KAAA;QAAAuL,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAxL,IAAA,GAAAqL,KAAA,CAAAjO,KAAA;UACA,IAAA4C,IAAA,CAAAnC,EAAA,KAAAA,EAAA;YACA,OAAAmC,IAAA;UACA;UACA,IAAAA,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;YACA,IAAA2M,KAAA,QAAA/D,YAAA,CAAA1H,IAAA,CAAA3C,QAAA,EAAAQ,EAAA;YACA,IAAA4N,KAAA;cACA,OAAAA,KAAA;YACA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;MACA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAX,MAAA;MACA,IAAAlL,IAAA,QAAA0H,YAAA,MAAA7L,eAAA,EAAAqP,MAAA;MACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA8L,UAAA,GAAA9L,IAAA,CAAA8L,UAAA;QACA9L,IAAA,CAAA8L,UAAA,CAAAvI,IAAA;UAAAwI,KAAA;UAAAC,QAAA;UAAAC,QAAA;QAAA;MACA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAhB,MAAA,EAAAiB,GAAA;MACA,IAAAnM,IAAA,QAAA0H,YAAA,MAAA7L,eAAA,EAAAqP,MAAA;MACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA8L,UAAA,GAAA9L,IAAA,CAAA8L,UAAA;QACA9L,IAAA,CAAA8L,UAAA,CAAAtD,MAAA,CAAA2D,GAAA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAlB,MAAA,EAAAiB,GAAA;MACA,IAAAnM,IAAA,QAAA0H,YAAA,MAAA7L,eAAA,EAAAqP,MAAA;MACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA8L,UAAA,GAAA9L,IAAA,CAAA8L,UAAA;QACA9L,IAAA,CAAA8L,UAAA,CAAAK,GAAA,EAAAH,QAAA,CAAAzI,IAAA;MACA;IACA;IACA;IACA8I,sBAAA,WAAAA,uBAAAnB,MAAA,EAAAiB,GAAA,EAAAG,IAAA;MACA,IAAAtM,IAAA,QAAA0H,YAAA,MAAA7L,eAAA,EAAAqP,MAAA;MACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAA8L,UAAA,GAAA9L,IAAA,CAAA8L,UAAA;QACA9L,IAAA,CAAA8L,UAAA,CAAAK,GAAA,EAAAH,QAAA,CAAAxD,MAAA,CAAA8D,IAAA;MACA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAArB,MAAA;MACA,IAAAlL,IAAA,QAAA0H,YAAA,MAAA7L,eAAA,EAAAqP,MAAA;MACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAAgM,QAAA,GAAAhM,IAAA,CAAAgM,QAAA;QACAhM,IAAA,CAAAgM,QAAA,CAAAzI,IAAA;MACA;IACA;IACA;IACAiJ,sBAAA,WAAAA,uBAAAtB,MAAA,EAAAoB,IAAA;MACA,IAAAtM,IAAA,QAAA0H,YAAA,MAAA7L,eAAA,EAAAqP,MAAA;MACA,IAAAlL,IAAA,IAAAA,IAAA,CAAAiD,IAAA;QACAjD,IAAA,CAAAgM,QAAA,GAAAhM,IAAA,CAAAgM,QAAA;QACAhM,IAAA,CAAAgM,QAAA,CAAAxD,MAAA,CAAA8D,IAAA;MACA;IACA;IACA;IACAG,0BAAA,WAAAA,2BAAAtE,IAAA,EAAAlF,IAAA,EAAA2D,GAAA;MAAA,IAAA8F,MAAA;MACA,KAAAvE,IAAA,CAAAlF,IAAA,CAAA8F,UAAA;QACA,KAAAf,QAAA,CAAAD,KAAA;QACA;MACA;MACA,IAAA4E,MAAA,OAAAC,UAAA;MACAD,MAAA,CAAAE,MAAA,aAAAlB,CAAA;QACAe,MAAA,CAAAhF,YAAA,CAAAgF,MAAA,CAAA7Q,eAAA,EAAAoH,IAAA,EAAA2D,GAAA,IAAA+E,CAAA,CAAAmB,MAAA,CAAAC,MAAA;MACA;MACAJ,MAAA,CAAAK,aAAA,CAAA7E,IAAA;MACA;IACA;IACA;IACA8E,2BAAA,WAAAA,4BAAA9E,IAAA,EAAAlF,IAAA,EAAA2D,GAAA;MACA;MACA,KAAAc,YAAA,MAAA7L,eAAA,EAAAoH,IAAA,EAAA2D,GAAA,IAAAuB,IAAA,CAAA1N,IAAA;MACA;IACA;IACAyS,oBAAA,WAAAA,qBAAAlN,IAAA;MACA,KAAAlE,YAAA,GAAAkE,IAAA;IACA;IACA;IACAyH,cAAA,WAAAA,eAAA0F,OAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,MAAA,GAAA5I,SAAA,CAAA3F,MAAA,QAAA2F,SAAA,QAAAa,SAAA,GAAAb,SAAA;MACA,KAAA0I,OAAA;MACA,IAAAG,GAAA,GAAA1K,KAAA,CAAAC,OAAA,CAAAsK,OAAA,IAAAA,OAAA,IAAAA,OAAA;MACA,OAAAG,GAAA,CAAAvN,GAAA,WAAAC,IAAA;QACA,IAAAuN,OAAA;UACA1P,EAAA,EAAAmC,IAAA,CAAApC,OAAA;UACA4P,WAAA,EAAAxN,IAAA,CAAAwN,WAAA;UACA/S,IAAA,EAAAuF,IAAA,CAAAhB,SAAA;UACA2D,IAAA,EAAA3C,IAAA,CAAAf,SAAA;UACAwO,CAAA,EAAAzN,IAAA,CAAAyN,CAAA;UACAC,CAAA,EAAA1N,IAAA,CAAA0N,CAAA;UACAzK,IAAA,EAAAjD,IAAA,CAAAiD,IAAA;UACArG,MAAA,EAAAoD,IAAA,CAAApD,MAAA,YAAAoD,IAAA,CAAApD,MAAA;UACA+Q,QAAA,EAAA3N,IAAA,CAAA2N,QAAA,aAAA3N,IAAA,CAAA2N,QAAA,KAAArI,SAAA,GAAAtF,IAAA,CAAA2N,QAAA;UACAC,eAAA,EAAA5N,IAAA,CAAA4N,eAAA,aAAA5N,IAAA,CAAA4N,eAAA,KAAAtI,SAAA,GAAAtF,IAAA,CAAA4N,eAAA;UACAC,kBAAA,EAAA7N,IAAA,CAAA6N,kBAAA,aAAA7N,IAAA,CAAA6N,kBAAA,KAAAvI,SAAA,GAAAtF,IAAA,CAAA6N,kBAAA;UACAC,gBAAA,EAAA9N,IAAA,CAAA8N,gBAAA;YACAjQ,EAAA,EAAAmC,IAAA,CAAA8N,gBAAA,CAAAjQ,EAAA;YACAoF,IAAA,EAAAjD,IAAA,CAAA8N,gBAAA,CAAA7K,IAAA;YACA8K,UAAA,EAAA/N,IAAA,CAAA8N,gBAAA,CAAAC,UAAA;YACAnR,MAAA,EAAAoD,IAAA,CAAA8N,gBAAA,CAAAlR,MAAA;YACAC,oBAAA,EAAAmD,IAAA,CAAA8N,gBAAA,CAAAjR,oBAAA;YACAC,iBAAA,EAAAkD,IAAA,CAAA8N,gBAAA,CAAAhR,iBAAA;UACA;YAAAe,EAAA;YAAAoF,IAAA;YAAA8K,UAAA;YAAAnR,MAAA;YAAAC,oBAAA;YAAAC,iBAAA;UAAA;UACAkR,SAAA,EAAAhO,IAAA,CAAAiO,gBAAA;YACAxT,IAAA,EAAAuF,IAAA,CAAAiO,gBAAA,CAAAxT,IAAA;YACAyT,mBAAA,EAAAlO,IAAA,CAAAiO,gBAAA,CAAAC,mBAAA;YACAC,mBAAA,EAAAnO,IAAA,CAAAiO,gBAAA,CAAAG,cAAA,GACApO,IAAA,CAAAiO,gBAAA,CAAAG,cAAA,CAAArO,GAAA,WAAAsO,CAAA;cAAA;gBACAxQ,EAAA,EAAAwQ,CAAA,CAAAxQ,EAAA;gBACAV,KAAA,EAAAkR,CAAA,CAAAC,GAAA;gBACAC,WAAA,EAAAF,CAAA,CAAAG,gBAAA,GAAAH,CAAA,CAAAG,gBAAA,CAAAzO,GAAA,WAAA0O,GAAA;kBAAA;oBACA5Q,EAAA,EAAA4Q,GAAA,CAAA5Q,EAAA;oBACA6Q,MAAA,EAAAD,GAAA,CAAAC,MAAA;oBACAjB,CAAA,EAAAgB,GAAA,CAAAE,MAAA;oBACAjB,CAAA,EAAAe,GAAA,CAAAG,MAAA;oBACAC,IAAA,EAAAJ,GAAA,CAAAI,IAAA;oBACAC,IAAA,EAAAL,GAAA,CAAAK,IAAA;oBACAC,MAAA,EAAAN,GAAA,CAAAM,MAAA;oBACAnR,OAAA,EAAAwP,MAAA,CAAA9F,iBAAA,CAAAmH,GAAA,CAAAO,aAAA;oBACA/P,SAAA,EAAAwP,GAAA,CAAAO,aAAA;kBACA;gBAAA;kBAAAnR,EAAA;kBAAA6Q,MAAA;kBAAAjB,CAAA;kBAAAC,CAAA;kBAAAmB,IAAA;kBAAAC,IAAA;kBAAAlR,OAAA;kBAAAqB,SAAA;gBAAA;gBACA4P,IAAA,EAAAR,CAAA,CAAAQ,IAAA;gBACAC,IAAA,EAAAT,CAAA,CAAAS,IAAA;gBACAG,KAAA,EAAAZ,CAAA,CAAAxR,oBAAA;gBACA4B,MAAA,EAAA4P,CAAA,CAAAvR,iBAAA;gBACAF,MAAA,EAAAyR,CAAA,CAAAzR,MAAA;gBACAqG,IAAA,EAAAoL,CAAA,CAAApL,IAAA;gBACA8K,UAAA,EAAAM,CAAA,CAAAN,UAAA;cACA;YAAA;YACAjC,UAAA,EAAA9L,IAAA,CAAAiO,gBAAA,CAAAiB,aAAA,GACAtM,KAAA,CAAAC,OAAA,CAAA7C,IAAA,CAAAiO,gBAAA,CAAAiB,aAAA,IAAAlP,IAAA,CAAAiO,gBAAA,CAAAiB,aAAA,CAAAnP,GAAA,WAAAoP,CAAA;cAAA;gBACAC,WAAA,EAAAD,CAAA,CAAAC,WAAA;gBACArD,KAAA,EAAAoD,CAAA,CAAAE,QAAA;gBACArD,QAAA,EAAAmD,CAAA,CAAAG,OAAA;gBACArD,QAAA,EAAAkD,CAAA,CAAAI,WAAA;cACA;YAAA;UACA;YAAA9U,IAAA;YAAAyT,mBAAA;YAAAC,mBAAA;YAAArC,UAAA;UAAA;UACA0D,QAAA,EAAAxP,IAAA,CAAAyP,SAAA;YACAhV,IAAA,EAAAuF,IAAA,CAAAyP,SAAA,CAAAhV,IAAA;YACAyT,mBAAA,EAAAlO,IAAA,CAAAyP,SAAA,CAAAvB,mBAAA;YACAC,mBAAA,EAAAnO,IAAA,CAAAyP,SAAA,CAAArB,cAAA,GACApO,IAAA,CAAAyP,SAAA,CAAArB,cAAA,CAAArO,GAAA,WAAAsO,CAAA;cAAA;gBACAxQ,EAAA,EAAAwQ,CAAA,CAAAxQ,EAAA;gBACAyQ,GAAA,EAAAD,CAAA,CAAAC,GAAA;gBACA1R,MAAA,EAAAyR,CAAA,CAAAzR,MAAA;gBACAqG,IAAA,EAAAoL,CAAA,CAAApL,IAAA;gBACA8K,UAAA,EAAAM,CAAA,CAAAN,UAAA;gBACAlR,oBAAA,EAAAwR,CAAA,CAAAxR,oBAAA;gBACAC,iBAAA,EAAAuR,CAAA,CAAAvR,iBAAA;gBACAyR,WAAA,EAAAF,CAAA,CAAAG,gBAAA,GAAAH,CAAA,CAAAG,gBAAA,CAAAzO,GAAA,WAAA0O,GAAA;kBAAA;oBACA5Q,EAAA,EAAA4Q,GAAA,CAAA5Q,EAAA;oBACA6Q,MAAA,EAAAD,GAAA,CAAAC,MAAA;oBACAjB,CAAA,EAAAgB,GAAA,CAAAE,MAAA;oBACAjB,CAAA,EAAAe,GAAA,CAAAG,MAAA;oBACAC,IAAA,EAAAJ,GAAA,CAAAI,IAAA;oBACAC,IAAA,EAAAL,GAAA,CAAAK,IAAA;oBACAC,MAAA,EAAAN,GAAA,CAAAM,MAAA;oBACAnR,OAAA,EAAAwP,MAAA,CAAA9F,iBAAA,CAAAmH,GAAA,CAAAO,aAAA;oBACA/P,SAAA,EAAAwP,GAAA,CAAAO,aAAA;kBACA;gBAAA;kBAAAnR,EAAA;kBAAA6Q,MAAA;kBAAAjB,CAAA;kBAAAC,CAAA;kBAAAmB,IAAA;kBAAAC,IAAA;kBAAAlR,OAAA;kBAAAqB,SAAA;gBAAA;cACA;YAAA;YACA6M,UAAA,EAAA9L,IAAA,CAAAyP,SAAA,CAAAP,aAAA,GACAtM,KAAA,CAAAC,OAAA,CAAA7C,IAAA,CAAAyP,SAAA,CAAAP,aAAA,IAAAlP,IAAA,CAAAyP,SAAA,CAAAP,aAAA,CAAAnP,GAAA,WAAAoP,CAAA;cAAA;gBACAC,WAAA,EAAAD,CAAA,CAAAC,WAAA;gBACArD,KAAA,EAAAoD,CAAA,CAAAE,QAAA;gBACArD,QAAA,EAAAmD,CAAA,CAAAG,OAAA;gBACArD,QAAA,EAAAkD,CAAA,CAAAI,WAAA;cACA;YAAA;UACA;YAAA9U,IAAA;YAAAyT,mBAAA;YAAAC,mBAAA;YAAArC,UAAA;UAAA;UACA4D,YAAA,EAAA1P,IAAA,CAAA2P,oBAAA;YACAP,WAAA,EAAApP,IAAA,CAAA2P,oBAAA,CAAAP,WAAA;YACAxS,MAAA,EAAAoD,IAAA,CAAA2P,oBAAA,CAAA/S,MAAA;YACAmP,KAAA,EAAA/L,IAAA,CAAA2P,oBAAA,CAAAN,QAAA;YACArD,QAAA,EAAAhM,IAAA,CAAA2P,oBAAA,CAAAL,OAAA;UACA;YAAAF,WAAA;YAAAxS,MAAA;YAAAmP,KAAA;YAAAC,QAAA;UAAA;UACA3O,QAAA;UACAgQ,MAAA,EAAAA;QACA;QACA;QACAE,OAAA,CAAAlQ,QAAA,GAAA2C,IAAA,CAAA3C,QAAA,GAAA+P,MAAA,CAAA3F,cAAA,CAAAzH,IAAA,CAAA3C,QAAA,EAAAkQ,OAAA;QACA,OAAAA,OAAA;MACA;IACA;IACAqC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9N,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA6N,SAAA;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAnL,eAAA,EAAA1J,YAAA,EAAA8U,qBAAA,EAAAC,uBAAA,EAAAC,UAAA,EAAAC,QAAA,EAAApL,YAAA,EAAAqL,OAAA,EAAAC,QAAA;QAAA,WAAAxO,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAqO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnO,IAAA,GAAAmO,SAAA,CAAAlO,IAAA;YAAA;cAAAkO,SAAA,CAAAnO,IAAA;cAEAsN,MAAA,CAAAlR,UAAA;cACA;cACAoG,eAAA,GAAA8K,MAAA,CAAA1U,YAAA,CAAA4K,IAAA,WAAAC,IAAA;gBAAA,OAAAtC,MAAA,CAAAsC,IAAA,CAAAnI,EAAA,MAAAgS,MAAA,CAAAzU,UAAA;cAAA;cACAC,YAAA,GAAA0J,eAAA,GAAAA,eAAA,CAAA1J,YAAA,SAEA;cACA8U,qBAAA,GAAAN,MAAA,CAAA/T,YAAA,GAAA+T,MAAA,CAAA/T,YAAA,CAAA+B,EAAA;cACAuS,uBAAA,GAAAP,MAAA,CAAA/T,YAAA,GAAA+T,MAAA,CAAA/T,YAAA,CAAArB,IAAA,SAEA;cACA4V,UAAA;gBACAM,UAAA,EAAAd,MAAA,CAAAzU,UAAA;gBACAC,YAAA,EAAAA,YAAA;gBAAA;gBACA6K,iBAAA,EAAA2J,MAAA,CAAAtU,IAAA,CAAA2K,iBAAA;gBACA1K,SAAA,EAAAqU,MAAA,CAAAtU,IAAA,CAAAC,SAAA;gBACAC,QAAA,EAAAoU,MAAA,CAAAtU,IAAA,CAAAE,QAAA;gBACA6K,OAAA,EAAAuJ,MAAA,CAAAvU,aAAA,GAAAuU,MAAA,CAAAvU,aAAA,CAAAgL,OAAA;gBACAzJ,oBAAA,EAAAgT,MAAA,CAAAtU,IAAA,CAAAG,QAAA;gBACAoB,iBAAA,EAAA+S,MAAA,CAAAtU,IAAA,CAAAI,SAAA;gBACAC,mBAAA,EAAAiU,MAAA,CAAAtU,IAAA,CAAAK,mBAAA;gBACAgV,qBAAA;kBACAxR,gBAAA,GAAA2Q,qBAAA,GAAAF,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,eAAA2U,qBAAA,eAAAA,qBAAA,CAAA3Q,gBAAA,IAAAwD,KAAA,CAAAC,OAAA,CAAAgN,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,EAAAgE,gBAAA,IACAyQ,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,EAAAgE,gBAAA,CAAAW,GAAA,WAAA8Q,IAAA;oBAAA;sBACAhT,EAAA,EAAAgT,IAAA,CAAAhT,EAAA;sBACAoF,IAAA;sBACAqL,GAAA,EAAAuC,IAAA,CAAAvC,GAAA;sBACAK,MAAA,EAAAkC,IAAA,CAAAlC,MAAA;sBACAC,MAAA,EAAAiC,IAAA,CAAAjC,MAAA;sBACAC,IAAA,EAAAgC,IAAA,CAAAhC,IAAA;sBACAC,IAAA,EAAA+B,IAAA,CAAA/B,IAAA;sBACAjS,oBAAA,EAAAgU,IAAA,CAAAhU,oBAAA;sBACAC,iBAAA,EAAA+T,IAAA,CAAA/T,iBAAA;sBACAF,MAAA;sBACAmR,UAAA;oBACA;kBAAA;kBACA1O,kBAAA;oBACAzC,MAAA,IAAAoT,sBAAA,GAAAH,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,eAAA4U,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAA3Q,kBAAA,cAAA2Q,sBAAA,uBAAAA,sBAAA,CAAApT,MAAA;oBACAE,iBAAA,IAAAmT,sBAAA,GAAAJ,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,eAAA6U,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAA5Q,kBAAA,cAAA4Q,sBAAA,uBAAAA,sBAAA,CAAAnT,iBAAA;oBACAE,oBAAA,GAAAkT,sBAAA,GAAAL,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,eAAA8U,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAA7Q,kBAAA,cAAA6Q,sBAAA,gBAAAA,sBAAA,GAAAA,sBAAA,CAAAlT,oBAAA,cAAAkT,sBAAA,eAAAA,sBAAA,CAAApR,MAAA,GACA+Q,MAAA,CAAA5R,kBAAA,CAAA4R,MAAA,CAAAzU,UAAA,EAAAiE,kBAAA,CAAArC,oBAAA,CAAA+C,GAAA,WAAApC,GAAA;sBAAA;wBACAoB,IAAA,EAAApB,GAAA,CAAAoB,IAAA;wBACAE,SAAA,EAAAtB,GAAA,CAAAsB,SAAA;wBACAD,SAAA,EAAArB,GAAA,CAAAqB,SAAA;sBACA;oBAAA;kBACA;gBACA;gBACA0H,wBAAA,EAAAmJ,MAAA,CAAAnT,UAAA,CAAAqD,GAAA,WAAA+Q,GAAA;kBACA,IAAAC,UAAA;oBACAlT,EAAA,EAAAiT,GAAA,CAAAjT,EAAA;oBACA8S,UAAA,EAAAd,MAAA,CAAAzU,UAAA;oBACAX,IAAA,EAAAqW,GAAA,CAAArW,IAAA;oBACAoM,OAAA,EAAAiK,GAAA,CAAAlK,GAAA;oBACAG,QAAA,EAAA+J,GAAA,CAAAhK,OAAA;oBACAL,MAAA,EAAAqK,GAAA,CAAArK,MAAA,IAAAqK,GAAA,CAAArW,IAAA;oBACA2M,aAAA;kBACA;kBAEA,IAAA0J,GAAA,CAAAlK,GAAA;oBACA,IAAAoK,kBAAA,GAAAnB,MAAA,CAAAoB,qBAAA,CAAApB,MAAA,CAAAhU,eAAA;oBACAkV,UAAA,CAAA1J,mBAAA;sBACAhI,kBAAA;wBACAzC,MAAA,EAAAiT,MAAA,CAAA9S,gBAAA,CAAAH,MAAA;wBACAE,iBAAA,EAAA+S,MAAA,CAAA9S,gBAAA,CAAAD,iBAAA;wBACAE,oBAAA,EAAA6S,MAAA,CAAA9S,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA,GAAA+Q,MAAA,CAAA9S,gBAAA,CAAAC,oBAAA;sBACA;sBACAwK,WAAA,EAAAwJ;oBACA;kBACA;oBACAD,UAAA,CAAA1J,mBAAA;kBACA;kBAEA,OAAA0J,UAAA;gBACA;gBACAG,kBAAA,EAAArB,MAAA,CAAA3R,oBAAA,CAAA2R,MAAA,CAAAzU,UAAA,KAAAwH,KAAA,CAAAC,OAAA,CAAAgN,MAAA,CAAA3R,oBAAA,CAAA2R,MAAA,CAAAzU,UAAA,KACAyU,MAAA,CAAA3R,oBAAA,CAAA2R,MAAA,CAAAzU,UAAA,EAAA2E,GAAA,WAAA3C,KAAA;kBAAA;oBACAS,EAAA,EAAAT,KAAA,CAAAS,EAAA;oBACAkQ,UAAA,EAAA3Q,KAAA,CAAA2Q,UAAA;oBACA9K,IAAA;oBACAqL,GAAA,EAAAlR,KAAA,CAAAkR,GAAA;oBACAzR,oBAAA,EAAAO,KAAA,CAAAP,oBAAA;oBACAC,iBAAA,EAAAM,KAAA,CAAAN,iBAAA;kBACA;gBAAA;gBACAqU,aAAA,EAAAtB,MAAA,CAAA1R,cAAA,CAAA0R,MAAA,CAAAzU,UAAA,KAAAwH,KAAA,CAAAC,OAAA,CAAAgN,MAAA,CAAA1R,cAAA,CAAA0R,MAAA,CAAAzU,UAAA,KACAyU,MAAA,CAAA1R,cAAA,CAAA0R,MAAA,CAAAzU,UAAA,EAAA2E,GAAA,WAAAqR,EAAA;kBAAA;oBACAvT,EAAA,EAAAuT,EAAA,CAAAvT,EAAA;oBACA8S,UAAA,EAAAS,EAAA,CAAAT,UAAA,IAAAd,MAAA,CAAAzU,UAAA;oBACA6H,IAAA,EAAAmO,EAAA,CAAAnO,IAAA;oBACA8K,UAAA,EAAAqD,EAAA,CAAArD,UAAA;oBACAtT,IAAA,EAAA2W,EAAA,CAAA3W,IAAA;oBACA4W,OAAA,EAAAD,EAAA,CAAAC,OAAA;kBACA;gBAAA;cACA;cAAAX,SAAA,CAAAlO,IAAA;cAAA,OAEA,IAAA8O,uBAAA,EAAAjB,UAAA;YAAA;cAAAC,QAAA,GAAAI,SAAA,CAAAhO,IAAA;cACAmN,MAAA,CAAA5G,MAAA,CAAAsI,UAAA;;cAEA;cAAAb,SAAA,CAAAlO,IAAA;cAAA,OACAqN,MAAA,CAAA9L,YAAA,CAAA8L,MAAA,CAAAzU,UAAA;YAAA;cAEA,IAAA+U,qBAAA,IAAAN,MAAA,CAAAhU,eAAA,CAAAiD,MAAA;gBACA;gBACAoG,YAAA,GAAA2K,MAAA,CAAAnI,YAAA,CAAAmI,MAAA,CAAAhU,eAAA,EAAAsU,qBAAA;gBAEA,IAAAjL,YAAA;kBACA;kBACAqL,OAAA;kBACAC,QAAA,YAAAA,SAAA1Q,KAAA,EAAA0R,QAAA;oBAAA,IAAAC,WAAA,GAAAhN,SAAA,CAAA3F,MAAA,QAAA2F,SAAA,QAAAa,SAAA,GAAAb,SAAA;oBAAA,IAAAiN,UAAA,OAAAtG,2BAAA,CAAA9K,OAAA,EACAR,KAAA;sBAAA6R,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAApG,CAAA,MAAAqG,MAAA,GAAAD,UAAA,CAAAnG,CAAA,IAAAC,IAAA;wBAAA,IAAAxL,IAAA,GAAA2R,MAAA,CAAAvU,KAAA;wBACA,IAAAwU,OAAA,MAAAnQ,MAAA,KAAAgC,mBAAA,CAAAnD,OAAA,EAAAmR,WAAA,IAAAzR,IAAA,CAAAnC,EAAA;wBACA,IAAAmC,IAAA,CAAAnC,EAAA,KAAA2T,QAAA;0BACAjB,OAAA,CAAAhN,IAAA,CAAAC,KAAA,CAAA+M,OAAA,MAAA9M,mBAAA,CAAAnD,OAAA,EAAAsR,OAAA;0BACA;wBACA;wBACA,IAAA5R,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;0BACA,IAAA0R,QAAA,CAAAxQ,IAAA,CAAA3C,QAAA,EAAAmU,QAAA,EAAAI,OAAA;4BACA;0BACA;wBACA;sBACA;oBAAA,SAAAlG,GAAA;sBAAAgG,UAAA,CAAA/F,CAAA,CAAAD,GAAA;oBAAA;sBAAAgG,UAAA,CAAA9F,CAAA;oBAAA;oBACA;kBACA;kBAEA4E,QAAA,CAAAX,MAAA,CAAAhU,eAAA,EAAAsU,qBAAA;kBACAN,MAAA,CAAAtR,gBAAA,GAAAgS,OAAA,CAAAsB,KAAA;;kBAEA;kBACAhC,MAAA,CAAA/T,YAAA,GAAAoJ,YAAA;;kBAEA;kBACA2K,MAAA,CAAAlM,SAAA;oBACAkM,MAAA,CAAAlM,SAAA;sBACA;sBACAkM,MAAA,CAAA3C,oBAAA,CAAAhI,YAAA;oBACA;kBACA;gBACA;cACA;cAEAxD,OAAA,CAAAC,GAAA,UAAA0O,UAAA;cAAAK,SAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,SAAA,CAAAnO,IAAA;cAAAmO,SAAA,CAAA5I,EAAA,GAAA4I,SAAA;cAEAhP,OAAA,CAAAqG,KAAA,UAAA2I,SAAA,CAAA5I,EAAA;cACA+H,MAAA,CAAA7H,QAAA,CAAAD,KAAA;YAAA;cAAA2I,SAAA,CAAAnO,IAAA;cAEAsN,MAAA,CAAAlR,UAAA;cAAA,OAAA+R,SAAA,CAAAzI,MAAA;YAAA;YAAA;cAAA,OAAAyI,SAAA,CAAA1M,IAAA;UAAA;QAAA,GAAA8L,QAAA;MAAA;IAEA;IACAgC,eAAA,WAAAA,gBAAA;MACA;MACA,UAAA/U,gBAAA,CAAAC,oBAAA,SAAAD,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA;QACA,KAAA/B,gBAAA,CAAAC,oBAAA;UAAA+B,IAAA;UAAAnB,OAAA;UAAAoB,SAAA;UAAAC,SAAA;QAAA;MACA;MACA,KAAAlC,gBAAA,CAAAC,oBAAA,CAAAuG,IAAA;QAAAxE,IAAA;QAAAnB,OAAA;QAAAoB,SAAA;QAAAC,SAAA;MAAA;IACA;IACA8S,kBAAA,WAAAA,mBAAA5F,GAAA;MACA,KAAApP,gBAAA,CAAAC,oBAAA,CAAAwL,MAAA,CAAA2D,GAAA;IACA;IACA;IACA6F,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,OAAAD,IAAA,CAAAlS,GAAA,WAAAiG,IAAA;QACA;QACA,IAAAhG,IAAA,OAAAK,cAAA,CAAAC,OAAA,MAAA0F,IAAA;;QAEA;QACA,IAAAhG,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;UACAkB,IAAA,CAAA3C,QAAA,GAAA6U,MAAA,CAAAF,kBAAA,CAAAhS,IAAA,CAAA3C,QAAA;QACA;UACA;UACA,OAAA2C,IAAA,CAAA3C,QAAA;QACA;QAEA,OAAA2C,IAAA;MACA;IACA;IACA8F,oBAAA,WAAAA,qBAAAjI,EAAA;MAAA,IAAAsU,MAAA;MAAA,WAAApQ,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAmQ,SAAA;QAAA,IAAArN,eAAA,EAAA1J,YAAA,EAAA8G,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAiQ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/P,IAAA,GAAA+P,SAAA,CAAA9P,IAAA;YAAA;cAAA8P,SAAA,CAAA/P,IAAA;cAEA;cACAwC,eAAA,GAAAoN,MAAA,CAAAhX,YAAA,CAAA4K,IAAA,WAAAC,IAAA;gBAAA,OAAAtC,MAAA,CAAAsC,IAAA,CAAAnI,EAAA,MAAAA,EAAA;cAAA;cACAxC,YAAA,GAAA0J,eAAA,GAAAA,eAAA,CAAA1J,YAAA;cAAAiX,SAAA,CAAA9P,IAAA;cAAA,OAEA,IAAA+P,0BAAA;gBAAAlX,YAAA,EAAAA;cAAA;YAAA;cAAA8G,GAAA,GAAAmQ,SAAA,CAAA5P,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAC,KAAA,CAAAC,OAAA,CAAAV,GAAA,CAAAnH,IAAA;gBACAmX,MAAA,CAAAlV,gBAAA,GAAAkV,MAAA,CAAAH,kBAAA,CAAA7P,GAAA,CAAAnH,IAAA;cACA;cAAAsX,SAAA,CAAA9P,IAAA;cAAA;YAAA;cAAA8P,SAAA,CAAA/P,IAAA;cAAA+P,SAAA,CAAAxK,EAAA,GAAAwK,SAAA;cAEA5Q,OAAA,CAAAqG,KAAA,aAAAuK,SAAA,CAAAxK,EAAA;YAAA;YAAA;cAAA,OAAAwK,SAAA,CAAAtO,IAAA;UAAA;QAAA,GAAAoO,QAAA;MAAA;IAEA;IACAI,yBAAA,WAAAA,0BAAA9S,GAAA,EAAAyM,GAAA;MACA;MACA,UAAApP,gBAAA,CAAAC,oBAAA,UAAAD,gBAAA,CAAAC,oBAAA,CAAAmP,GAAA;QACA;MACA;MAEA,IAAAsG,SAAA,YAAAA,UAAAR,IAAA,EAAApU,EAAA;QAAA,IAAA6U,UAAA,OAAAtH,2BAAA,CAAA9K,OAAA,EACA2R,IAAA;UAAAU,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAApH,CAAA,MAAAqH,MAAA,GAAAD,UAAA,CAAAnH,CAAA,IAAAC,IAAA;YAAA,IAAAxL,KAAA,GAAA2S,MAAA,CAAAvV,KAAA;YACA,IAAA4C,KAAA,CAAAnC,EAAA,KAAAA,EAAA,SAAAmC,KAAA;YACA,IAAAA,KAAA,CAAA3C,QAAA,IAAA2C,KAAA,CAAA3C,QAAA,CAAAyB,MAAA;cACA,IAAA2M,KAAA,GAAAgH,SAAA,CAAAzS,KAAA,CAAA3C,QAAA,EAAAQ,EAAA;cACA,IAAA4N,KAAA,SAAAA,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAgH,UAAA,CAAA/G,CAAA,CAAAD,GAAA;QAAA;UAAAgH,UAAA,CAAA9G,CAAA;QAAA;QACA;MACA;MACA,IAAA5L,IAAA,GAAAyS,SAAA,MAAAxV,gBAAA,EAAAyC,GAAA;MACA,IAAAM,IAAA;QACA;QACA,KAAAjD,gBAAA,CAAAC,oBAAA,CAAAmP,GAAA,EAAAvO,OAAA,GAAA8B,GAAA;QACA,KAAA3C,gBAAA,CAAAC,oBAAA,CAAAmP,GAAA,EAAAnN,SAAA,GAAAgB,IAAA,CAAAhB,SAAA;QACA,KAAAjC,gBAAA,CAAAC,oBAAA,CAAAmP,GAAA,EAAAlN,SAAA,GAAAe,IAAA,CAAAf,SAAA;MACA;IACA;IACA2T,eAAA,WAAAA,gBAAA/U,EAAA,EAAAgV,UAAA;MACA;MACA,YAAA9V,gBAAA,CAAAC,oBAAA,CAAAU,IAAA,WAAAC,GAAA,EAAAwO,GAAA;QAAA,OAAAA,GAAA,KAAA0G,UAAA,IAAAlV,GAAA,CAAAC,OAAA,KAAAC,EAAA;MAAA;IACA;IACA;IACAoT,qBAAA,WAAAA,sBAAA6B,SAAA;MAAA,IAAAC,OAAA;MACArR,OAAA,CAAAC,GAAA,WAAAmR,SAAA;MACA,OAAAA,SAAA,CAAA/S,GAAA,WAAAC,IAAA;QAAA;UACAwN,WAAA,EAAAxN,IAAA,CAAAwN,WAAA;UACA5P,OAAA,EAAAoC,IAAA,CAAAnC,EAAA;UACAmV,OAAA,EAAAhT,IAAA,CAAAqN,MAAA,GAAArN,IAAA,CAAAqN,MAAA,CAAAxP,EAAA;UACAmB,SAAA,EAAAgB,IAAA,CAAAvF,IAAA;UACAwE,SAAA,EAAAe,IAAA,CAAA2C,IAAA;UACA8K,CAAA,EAAAzN,IAAA,CAAAyN,CAAA;UACAC,CAAA,EAAA1N,IAAA,CAAA0N,CAAA;UACAzK,IAAA,EAAAjD,IAAA,CAAAiD,IAAA;UACArG,MAAA,EAAAoD,IAAA,CAAApD,MAAA;UACA+Q,QAAA,EAAA3N,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,OAAAkB,IAAA,CAAA2N,QAAA;UACAC,eAAA,EAAA5N,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,OAAAkB,IAAA,CAAA4N,eAAA;UACAC,kBAAA,EAAA7N,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,OAAAkB,IAAA,CAAA6N,kBAAA;UACAC,gBAAA,EAAA9N,IAAA,CAAA8N,gBAAA;YACAjQ,EAAA,EAAAmC,IAAA,CAAA8N,gBAAA,CAAAjQ,EAAA;YACAoF,IAAA,EAAAjD,IAAA,CAAA8N,gBAAA,CAAA7K,IAAA;YACA8K,UAAA,EAAA/N,IAAA,CAAA8N,gBAAA,CAAAC,UAAA;YACAnR,MAAA,EAAAoD,IAAA,CAAA8N,gBAAA,CAAAlR,MAAA;YACAC,oBAAA,EAAAmD,IAAA,CAAA8N,gBAAA,CAAAjR,oBAAA;YACAC,iBAAA,EAAAkD,IAAA,CAAA8N,gBAAA,CAAAhR,iBAAA;UACA;UACAmR,gBAAA,EAAAjO,IAAA,CAAAgO,SAAA;YACAvT,IAAA,EAAAuF,IAAA,CAAAgO,SAAA,CAAAvT,IAAA;YACAyT,mBAAA,EAAAlO,IAAA,CAAAgO,SAAA,CAAAE,mBAAA;YACAE,cAAA,EAAApO,IAAA,CAAAgO,SAAA,CAAAG,mBAAA,IAAAnO,IAAA,CAAAgO,SAAA,CAAAG,mBAAA,CAAArP,MAAA,GACAkB,IAAA,CAAAgO,SAAA,CAAAG,mBAAA,CAAApO,GAAA,WAAAkT,QAAA;cAAA;gBACApV,EAAA,EAAAoV,QAAA,CAAApV,EAAA;gBACAyQ,GAAA,EAAA2E,QAAA,CAAA9V,KAAA;gBACA0R,IAAA,EAAAoE,QAAA,CAAApE,IAAA;gBACAC,IAAA,EAAAmE,QAAA,CAAAnE,IAAA;gBACAlS,MAAA,EAAAqW,QAAA,CAAArW,MAAA;gBACAqG,IAAA;gBACA8K,UAAA,EAAAkF,QAAA,CAAAlF,UAAA;gBACAlR,oBAAA,EAAAoW,QAAA,CAAAhE,KAAA;gBACAnS,iBAAA,EAAAmW,QAAA,CAAAxU,MAAA;gBACA+P,gBAAA,EAAAyE,QAAA,CAAA1E,WAAA,IAAA0E,QAAA,CAAA1E,WAAA,CAAAzP,MAAA,GACAmU,QAAA,CAAA1E,WAAA,CAAAxO,GAAA,WAAAmT,KAAA;kBAAA;oBACArV,EAAA,EAAAqV,KAAA,CAAArV,EAAA;oBACA6Q,MAAA,EAAAwE,KAAA,CAAAxE,MAAA;oBACAC,MAAA,EAAAuE,KAAA,CAAAzF,CAAA,KAAAnI,SAAA,IAAA4N,KAAA,CAAAzF,CAAA,YAAAyF,KAAA,CAAAzF,CAAA,eAAAyF,KAAA,CAAAzF,CAAA;oBACAmB,MAAA,EAAAsE,KAAA,CAAAxF,CAAA,KAAApI,SAAA,IAAA4N,KAAA,CAAAxF,CAAA,YAAAwF,KAAA,CAAAxF,CAAA,eAAAwF,KAAA,CAAAxF,CAAA;oBACAmB,IAAA,EAAAqE,KAAA,CAAArE,IAAA,KAAAvJ,SAAA,IAAA4N,KAAA,CAAArE,IAAA,YAAAqE,KAAA,CAAArE,IAAA,WAAAqE,KAAA,CAAArE,IAAA,cAAAqE,KAAA,CAAArE,IAAA;oBACAC,IAAA,EAAAoE,KAAA,CAAApE,IAAA,KAAAxJ,SAAA,IAAA4N,KAAA,CAAApE,IAAA,YAAAoE,KAAA,CAAApE,IAAA,WAAAoE,KAAA,CAAApE,IAAA,cAAAoE,KAAA,CAAApE,IAAA;oBACAC,MAAA,EAAAmE,KAAA,CAAAnE,MAAA,KAAAzJ,SAAA,IAAA4N,KAAA,CAAAnE,MAAA,YAAAmE,KAAA,CAAAnE,MAAA,eAAAmE,KAAA,CAAAnE,MAAA;oBACAC,aAAA,EAAAkE,KAAA,CAAAjU,SAAA,KAAAqG,SAAA,IAAA4N,KAAA,CAAAjU,SAAA,YAAAiU,KAAA,CAAAjU,SAAA,eAAAiU,KAAA,CAAAjU,SAAA;kBACA;gBAAA;cACA;YAAA;YACAiQ,aAAA,EAAAlP,IAAA,CAAAgO,SAAA,CAAAlC,UAAA,IAAA9L,IAAA,CAAAgO,SAAA,CAAAlC,UAAA,CAAAhN,MAAA,GACAkB,IAAA,CAAAgO,SAAA,CAAAlC,UAAA,CAAA/L,GAAA,WAAAoT,IAAA;cAAA;gBACA/D,WAAA,EAAA+D,IAAA,CAAA/D,WAAA;gBACAC,QAAA,EAAA8D,IAAA,CAAApH,KAAA;gBACAuD,OAAA,EAAA6D,IAAA,CAAAnH,QAAA;gBACAuD,WAAA,EAAA4D,IAAA,CAAAlH,QAAA;cACA;YAAA;UACA;UACAwD,SAAA,EAAAzP,IAAA,CAAAwP,QAAA;YACA/U,IAAA,EAAAuF,IAAA,CAAAwP,QAAA,CAAA/U,IAAA;YACAyT,mBAAA,EAAAlO,IAAA,CAAAwP,QAAA,CAAAtB,mBAAA;YACAE,cAAA,EAAApO,IAAA,CAAAwP,QAAA,CAAArB,mBAAA,IAAAnO,IAAA,CAAAwP,QAAA,CAAArB,mBAAA,CAAArP,MAAA,GACAkB,IAAA,CAAAwP,QAAA,CAAArB,mBAAA,CAAApO,GAAA,WAAAkT,QAAA;cAAA;gBACApV,EAAA,EAAAoV,QAAA,CAAApV,EAAA;gBACAyQ,GAAA,EAAA2E,QAAA,CAAA3E,GAAA;gBACA1R,MAAA,EAAAqW,QAAA,CAAArW,MAAA;gBACAqG,IAAA;gBACA8K,UAAA,EAAAkF,QAAA,CAAAlF,UAAA;gBACAlR,oBAAA,EAAAoW,QAAA,CAAAhE,KAAA;gBACAnS,iBAAA,EAAAmW,QAAA,CAAAxU,MAAA;gBACA+P,gBAAA,EAAAyE,QAAA,CAAA1E,WAAA,IAAA0E,QAAA,CAAA1E,WAAA,CAAAzP,MAAA,GACAmU,QAAA,CAAA1E,WAAA,CAAAxO,GAAA,WAAAmT,KAAA;kBAAA;oBACArV,EAAA,EAAAqV,KAAA,CAAArV,EAAA;oBACA6Q,MAAA,EAAAwE,KAAA,CAAAxE,MAAA;oBACAC,MAAA,EAAAuE,KAAA,CAAAzF,CAAA,KAAAnI,SAAA,IAAA4N,KAAA,CAAAzF,CAAA,YAAAyF,KAAA,CAAAzF,CAAA,eAAAyF,KAAA,CAAAzF,CAAA;oBACAmB,MAAA,EAAAsE,KAAA,CAAAxF,CAAA,KAAApI,SAAA,IAAA4N,KAAA,CAAAxF,CAAA,YAAAwF,KAAA,CAAAxF,CAAA,eAAAwF,KAAA,CAAAxF,CAAA;oBACAmB,IAAA,EAAAqE,KAAA,CAAArE,IAAA,KAAAvJ,SAAA,IAAA4N,KAAA,CAAArE,IAAA,YAAAqE,KAAA,CAAArE,IAAA,WAAAqE,KAAA,CAAArE,IAAA,cAAAqE,KAAA,CAAArE,IAAA;oBACAC,IAAA,EAAAoE,KAAA,CAAApE,IAAA,KAAAxJ,SAAA,IAAA4N,KAAA,CAAApE,IAAA,YAAAoE,KAAA,CAAApE,IAAA,WAAAoE,KAAA,CAAApE,IAAA,cAAAoE,KAAA,CAAApE,IAAA;oBACAC,MAAA,EAAAmE,KAAA,CAAAnE,MAAA,KAAAzJ,SAAA,IAAA4N,KAAA,CAAAnE,MAAA,YAAAmE,KAAA,CAAAnE,MAAA,eAAAmE,KAAA,CAAAnE,MAAA;oBACAC,aAAA,EAAAkE,KAAA,CAAAjU,SAAA,KAAAqG,SAAA,IAAA4N,KAAA,CAAAjU,SAAA,YAAAiU,KAAA,CAAAjU,SAAA,eAAAiU,KAAA,CAAAjU,SAAA;kBACA;gBAAA;cACA;YAAA;YACAiQ,aAAA,EAAAlP,IAAA,CAAAwP,QAAA,CAAA1D,UAAA,IAAA9L,IAAA,CAAAwP,QAAA,CAAA1D,UAAA,CAAAhN,MAAA,GACAkB,IAAA,CAAAwP,QAAA,CAAA1D,UAAA,CAAA/L,GAAA,WAAAoT,IAAA;cAAA;gBACA/D,WAAA,EAAA+D,IAAA,CAAA/D,WAAA;gBACAC,QAAA,EAAA8D,IAAA,CAAApH,KAAA;gBACAuD,OAAA,EAAA6D,IAAA,CAAAnH,QAAA;gBACAuD,WAAA,EAAA4D,IAAA,CAAAlH,QAAA;cACA;YAAA;UACA;UACA0D,oBAAA,EAAA3P,IAAA,CAAA0P,YAAA;YACAN,WAAA,EAAApP,IAAA,CAAA0P,YAAA,CAAAN,WAAA;YACAxS,MAAA,EAAAoD,IAAA,CAAA0P,YAAA,CAAA9S,MAAA;YACAyS,QAAA,EAAArP,IAAA,CAAA0P,YAAA,CAAA3D,KAAA;YACAuD,OAAA,EAAAtP,IAAA,CAAA0P,YAAA,CAAA1D,QAAA,IAAAhM,IAAA,CAAA0P,YAAA,CAAA1D,QAAA,CAAAlN,MAAA,GAAAkB,IAAA,CAAA0P,YAAA,CAAA1D,QAAA;UACA;UACA3O,QAAA,EAAA2C,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA,GAAAiU,OAAA,CAAA9B,qBAAA,CAAAjR,IAAA,CAAA3C,QAAA;QACA;MAAA;IACA;IACA+V,gBAAA,WAAAA,iBAAA1T,GAAA,EAAAyM,GAAA;MACA;MACA,UAAApP,gBAAA,CAAAC,oBAAA,SAAAD,gBAAA,CAAAC,oBAAA,CAAA8B,MAAA;QACA,KAAA/B,gBAAA,CAAAC,oBAAA;UAAA+B,IAAA;UAAAnB,OAAA;UAAAoB,SAAA;UAAAC,SAAA;QAAA;MACA;MACA;MACA,SAAAlC,gBAAA,CAAAC,oBAAA,CAAAmP,GAAA;QACA,KAAApP,gBAAA,CAAAC,oBAAA,CAAAmP,GAAA,EAAApN,IAAA,GAAAW,GAAA;MACA;IACA;IACA;IACA4H,iBAAA,WAAAA,kBAAArI,SAAA;MACA,KAAAA,SAAA,UAAAhC,gBAAA;MAEA,IAAAoW,UAAA,YAAAA,WAAApB,IAAA;QAAA,IAAAqB,UAAA,OAAAlI,2BAAA,CAAA9K,OAAA,EACA2R,IAAA;UAAAsB,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAhI,CAAA,MAAAiI,MAAA,GAAAD,UAAA,CAAA/H,CAAA,IAAAC,IAAA;YAAA,IAAAxL,IAAA,GAAAuT,MAAA,CAAAnW,KAAA;YACA,IAAA4C,IAAA,CAAAf,SAAA,KAAAA,SAAA;cACA,OAAAe,IAAA,CAAAnC,EAAA;YACA;YACA,IAAAmC,IAAA,CAAA3C,QAAA,IAAA2C,IAAA,CAAA3C,QAAA,CAAAyB,MAAA;cACA,IAAA2M,KAAA,GAAA4H,UAAA,CAAArT,IAAA,CAAA3C,QAAA;cACA,IAAAoO,KAAA,SAAAA,KAAA;YACA;UACA;QAAA,SAAAC,GAAA;UAAA4H,UAAA,CAAA3H,CAAA,CAAAD,GAAA;QAAA;UAAA4H,UAAA,CAAA1H,CAAA;QAAA;QACA;MACA;MAEA,OAAAyH,UAAA,MAAApW,gBAAA;IACA;IACA;IACAuW,kBAAA,WAAAA,mBAAArL,IAAA,EAAAsL,QAAA;MACA,KAAAlY,IAAA,CAAAI,SAAA;MACA,KAAAJ,IAAA,CAAAG,QAAA;MACA,KAAAoC,UAAA;MACA,KAAAkK,QAAA,CAAAsB,OAAA;IACA;IACA;IACAnD,gBAAA,WAAAA,iBAAA;MACA,SAAA5K,IAAA,CAAAI,SAAA;QACA,IAAAiO,QAAA,QAAArO,IAAA,CAAAI,SAAA,CAAAuO,KAAA,MAAAC,GAAA;QACA,KAAArM,UAAA;UACArD,IAAA,EAAAmP,QAAA;UACAQ,GAAA,OAAA7O,IAAA,CAAAI,SAAA;UACA0O,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAzM,UAAA;MACA;IACA;IACA;IACA4V,8BAAA,WAAAA,+BAAAvL,IAAA,EAAAsL,QAAA;MACA,KAAA9W,cAAA,CAAAG,iBAAA;MACA,KAAAH,cAAA,CAAAE,oBAAA;MACA,KAAA6N,sBAAA;MACA,KAAA1C,QAAA,CAAAsB,OAAA;IACA;IACA;IACAqK,4BAAA,WAAAA,6BAAA;MACA,SAAAhX,cAAA,CAAAG,iBAAA;QACA,IAAA8M,QAAA,QAAAjN,cAAA,CAAAG,iBAAA,CAAAoN,KAAA,MAAAC,GAAA;QACA,KAAAO,sBAAA;UACAjQ,IAAA,EAAAmP,QAAA;UACAQ,GAAA,OAAAzN,cAAA,CAAAG,iBAAA;UACAuN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAG,sBAAA;MACA;IACA;IACA;IACAkJ,gCAAA,WAAAA,iCAAAzL,IAAA,EAAAsL,QAAA;MACA,KAAA1W,gBAAA,CAAAD,iBAAA;MACA,KAAAiB,wBAAA;MACA,KAAAiK,QAAA,CAAAsB,OAAA;IACA;IACA;IACA/B,8BAAA,WAAAA,+BAAA;MACA,SAAAxK,gBAAA,CAAAD,iBAAA;QACA,IAAA8M,QAAA,QAAA7M,gBAAA,CAAAD,iBAAA,CAAAoN,KAAA,MAAAC,GAAA;QACA,KAAApM,wBAAA;UACAtD,IAAA,EAAAmP,QAAA;UACAQ,GAAA,OAAArN,gBAAA,CAAAD,iBAAA;UACAuN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAxM,wBAAA;MACA;IACA;IACA;IACA8V,mBAAA,WAAAA,oBAAA1L,IAAA,EAAAsL,QAAA;MACA,KAAAlY,IAAA,CAAAK,mBAAA;MACA,KAAAoC,WAAA;MACA,KAAAgK,QAAA,CAAAsB,OAAA;IACA;IAEA;IACAlD,iBAAA,WAAAA,kBAAA;MACA,SAAA7K,IAAA,CAAAK,mBAAA;QACA,IAAAgO,QAAA,QAAArO,IAAA,CAAAK,mBAAA,CAAAsO,KAAA,MAAAC,GAAA;QACA,KAAAnM,WAAA;UACAvD,IAAA,EAAAmP,QAAA;UACAQ,GAAA,OAAA7O,IAAA,CAAAK,mBAAA;UACAyO,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;QACA,KAAAvM,WAAA;MACA;IACA;IACA;IACA8V,YAAA,WAAAA,aAAA1J,GAAA;MACA,IAAAA,GAAA;QACA,KAAA/L,eAAA,GAAA+L,GAAA;QACA,KAAAhM,cAAA;MACA;IACA;IACA2V,YAAA,WAAAA,aAAA;MACA,KAAA3V,cAAA;MACA,KAAAC,eAAA;IACA;IACA;IACA2V,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnR,IAAA;MACA,GAAAoR,IAAA;QACAJ,OAAA,CAAA1Y,IAAA,CAAAG,QAAA;QACAuY,OAAA,CAAAjM,QAAA,CAAAsB,OAAA;MACA,GAAAgL,KAAA;IACA;IACAC,mBAAA,WAAAA,oBAAApM,IAAA;MAAA,IAAAqM,OAAA;MAAA,WAAAzS,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAwS,SAAA;QAAA,IAAA7L,QAAA,EAAAzG,GAAA,EAAAyH,QAAA;QAAA,WAAA5H,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAsS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApS,IAAA,GAAAoS,SAAA,CAAAnS,IAAA;YAAA;cAAA,IAEA2F,IAAA,CAAA1N,IAAA,CAAA0F,WAAA,GAAAsK,QAAA;gBAAAkK,SAAA,CAAAnS,IAAA;gBAAA;cAAA;cACAgS,OAAA,CAAAxM,QAAA,CAAAD,KAAA;cAAA,OAAA4M,SAAA,CAAA3L,MAAA,WACA;YAAA;cAAA,MAIAb,IAAA,CAAAyM,IAAA;gBAAAD,SAAA,CAAAnS,IAAA;gBAAA;cAAA;cACAgS,OAAA,CAAAxM,QAAA,CAAAD,KAAA;cAAA,OAAA4M,SAAA,CAAA3L,MAAA,WACA;YAAA;cAAA2L,SAAA,CAAApS,IAAA;cAIAiS,OAAA,CAAAvL,MAAA,CAAAlN,OAAA;cACA6M,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,SAAAhB,IAAA;cACAS,QAAA,CAAAO,MAAA,iBAAAqL,OAAA,CAAAnZ,YAAA;cAAAsZ,SAAA,CAAAnS,IAAA;cAAA,OAEA,IAAA4G,0BAAA,EAAAR,QAAA;YAAA;cAAAzG,GAAA,GAAAwS,SAAA,CAAAjS,IAAA;cACA,IAAAP,GAAA,CAAAQ,IAAA,UAAAR,GAAA,CAAAnH,IAAA;gBACA;gBACAwZ,OAAA,CAAAjZ,IAAA,CAAAK,mBAAA,GAAAuG,GAAA,CAAAnH,IAAA,CAAAqO,OAAA;;gBAEA;gBACAO,QAAA,GAAAzH,GAAA,CAAAnH,IAAA,CAAAqO,OAAA,CAAAa,KAAA,MAAAC,GAAA;gBACAqK,OAAA,CAAAxW,WAAA;kBACAvD,IAAA,EAAAmP,QAAA;kBACAQ,GAAA,EAAAjI,GAAA,CAAAnH,IAAA,CAAAqO,OAAA;kBACAgB,GAAA,EAAAC,IAAA,CAAAC,GAAA;gBACA;gBAEAiK,OAAA,CAAAxM,QAAA,CAAAsB,OAAA;cACA;gBACAkL,OAAA,CAAAxM,QAAA,CAAAD,KAAA,CAAA5F,GAAA,CAAAoH,GAAA;cACA;cAAAoL,SAAA,CAAAnS,IAAA;cAAA;YAAA;cAAAmS,SAAA,CAAApS,IAAA;cAAAoS,SAAA,CAAA7M,EAAA,GAAA6M,SAAA;cAEAH,OAAA,CAAAxM,QAAA,CAAAD,KAAA;YAAA;cAAA4M,SAAA,CAAApS,IAAA;cAEAiS,OAAA,CAAAvL,MAAA,CAAAO,YAAA;cAAA,OAAAmL,SAAA,CAAA1M,MAAA;YAAA;cAAA,OAAA0M,SAAA,CAAA3L,MAAA,WAEA;YAAA;YAAA;cAAA,OAAA2L,SAAA,CAAA3Q,IAAA;UAAA;QAAA,GAAAyQ,QAAA;MAAA;IACA;IACA;IACAI,aAAA,WAAAA,cAAAC,KAAA;MACA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAC,cAAA;QACA,KAAAxZ,IAAA,CAAAG,QAAA,GAAAoZ,KAAA,CAAAC,cAAA;MACA;IACA;IACA;IACAC,0BAAA,WAAAA,2BAAAzG,WAAA;MACA,KAAAA,WAAA,KAAA3L,KAAA,CAAAC,OAAA,CAAA0L,WAAA;QACA;UAAAI,MAAA;UAAAC,MAAA;QAAA;MACA;MAEA,IAAAqG,OAAA,GAAA1G,WAAA,CAAAxO,GAAA,WAAAmT,KAAA;QAAA,OAAAA,KAAA,CAAAzF,CAAA;MAAA,GAAAyH,IAAA;MACA,IAAAC,OAAA,GAAA5G,WAAA,CAAAxO,GAAA,WAAAmT,KAAA;QAAA,OAAAA,KAAA,CAAAxF,CAAA;MAAA,GAAAwH,IAAA;MAEA;QACAvG,MAAA,EAAAsG,OAAA;QACArG,MAAA,EAAAuG;MACA;IACA;IACA;IACAC,uBAAA,WAAAA,wBAAAzG,MAAA,EAAAC,MAAA;MACA,IAAAyG,MAAA,GAAA1G,MAAA,GAAAA,MAAA,CAAAzE,KAAA;MACA,IAAAoL,MAAA,GAAA1G,MAAA,GAAAA,MAAA,CAAA1E,KAAA;;MAEA;MACA,IAAAqL,SAAA,GAAAC,IAAA,CAAAC,GAAA,CAAAJ,MAAA,CAAAvW,MAAA,EAAAwW,MAAA,CAAAxW,MAAA;MACA,IAAAyP,WAAA;MAEA,SAAAmH,CAAA,MAAAA,CAAA,GAAAH,SAAA,EAAAG,CAAA;QACAnH,WAAA,CAAAhL,IAAA;UACAkK,CAAA,EAAA4H,MAAA,CAAAK,CAAA;UACAhI,CAAA,EAAA4H,MAAA,CAAAI,CAAA;QACA;MACA;;MAEA;MACA,OAAAnH,WAAA,CAAAzP,MAAA,OAAAyP,WAAA;QAAAd,CAAA;QAAAC,CAAA;MAAA;IACA;IACA;IACAiI,cAAA,WAAAA,eAAApN,KAAA;MAAA,IAAAqN,OAAA;MACA,IAAAC,QAAA,QAAAnZ,UAAA,CAAA6L,KAAA;MACAsN,QAAA,CAAA7O,OAAA;MACA6O,QAAA,CAAA5O,WAAA,GAAA4O,QAAA,CAAApb,IAAA;;MAEA;MACA,KAAAkJ,SAAA;QACA;QACA,IAAAmS,QAAA,GAAAF,OAAA,CAAAhS,KAAA,eAAAnC,MAAA,CAAA8G,KAAA;QACA,IAAAuN,QAAA,IAAAA,QAAA;UACAA,QAAA,IAAAC,KAAA;UACAD,QAAA,IAAAE,MAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA1N,KAAA;MACA,IAAAsN,QAAA,QAAAnZ,UAAA,CAAA6L,KAAA;MACA,IAAAsN,QAAA,CAAA5O,WAAA,IAAA4O,QAAA,CAAA5O,WAAA,CAAAiP,IAAA;QACAL,QAAA,CAAApb,IAAA,GAAAob,QAAA,CAAA5O,WAAA,CAAAiP,IAAA;MACA;MACAL,QAAA,CAAA7O,OAAA;MACA6O,QAAA,CAAA5O,WAAA;IACA;IAEA;IACAkP,eAAA,WAAAA,gBAAA5N,KAAA;MACA,IAAAsN,QAAA,QAAAnZ,UAAA,CAAA6L,KAAA;MACAsN,QAAA,CAAA7O,OAAA;MACA6O,QAAA,CAAA5O,WAAA;IACA;IACA;IACAmP,aAAA,WAAAA,cAAAnT,IAAA,EAAAoT,IAAA;MACA,KAAA9W,IAAA,MAAAf,WAAA,EAAAyE,IAAA,EAAAoT,IAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAlZ,KAAA;MACA,KAAAU,UAAA;MACA,IAAAV,KAAA;QACA,IAAAwM,QAAA,GAAAxM,KAAA,CAAA8M,KAAA,MAAAC,GAAA;QACA,KAAArM,UAAA;UACArD,IAAA,EAAAmP,QAAA;UACAQ,GAAA,EAAAhN,KAAA;UACAiN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAgM,8BAAA,WAAAA,+BAAAnZ,KAAA;MACA,KAAAW,wBAAA;MACA,IAAAX,KAAA;QACA,IAAAwM,QAAA,GAAAxM,KAAA,CAAA8M,KAAA,MAAAC,GAAA;QACA,KAAApM,wBAAA;UACAtD,IAAA,EAAAmP,QAAA;UACAQ,GAAA,EAAAhN,KAAA;UACAiN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAiM,4BAAA,WAAAA,6BAAApZ,KAAA;MACA,KAAAsN,sBAAA;MACA,IAAAtN,KAAA;QACA,IAAAwM,QAAA,GAAAxM,KAAA,CAAA8M,KAAA,MAAAC,GAAA;QACA,KAAAO,sBAAA;UACAjQ,IAAA,EAAAmP,QAAA;UACAQ,GAAA,EAAAhN,KAAA;UACAiN,GAAA,EAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;IACA;IACAkM,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3U,kBAAA,CAAAzB,OAAA,mBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA0U,SAAA;QAAA,IAAA/N,QAAA,EAAAzG,GAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA8B,IAAA,UAAAwU,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtU,IAAA,GAAAsU,SAAA,CAAArU,IAAA;YAAA;cAAA,IACAkU,OAAA,CAAAnb,IAAA,CAAA2K,iBAAA;gBAAA2Q,SAAA,CAAArU,IAAA;gBAAA;cAAA;cACAkU,OAAA,CAAA1O,QAAA,CAAA8O,OAAA;cAAA,OAAAD,SAAA,CAAA7N,MAAA;YAAA;cAAA6N,SAAA,CAAAtU,IAAA;cAKAmU,OAAA,CAAAhY,aAAA;cACAgY,OAAA,CAAAzN,MAAA,CAAAlN,OAAA;;cAEA;cACA6M,QAAA,OAAAM,QAAA;cACAN,QAAA,CAAAO,MAAA,iBAAAuN,OAAA,CAAAnb,IAAA,CAAA2K,iBAAA;cAAA2Q,SAAA,CAAArU,IAAA;cAAA,OAEA,IAAAuU,8BAAA,EAAAnO,QAAA;YAAA;cAAAzG,GAAA,GAAA0U,SAAA,CAAAnU,IAAA;cAEA,IAAAP,GAAA,CAAAQ,IAAA;gBACA+T,OAAA,CAAA1O,QAAA,CAAAsB,OAAA,CAAAnH,GAAA,CAAAoH,GAAA;cACA;gBACAmN,OAAA,CAAA1O,QAAA,CAAAD,KAAA,CAAA5F,GAAA,CAAAoH,GAAA;cACA;cAAAsN,SAAA,CAAArU,IAAA;cAAA;YAAA;cAAAqU,SAAA,CAAAtU,IAAA;cAAAsU,SAAA,CAAA/O,EAAA,GAAA+O,SAAA;cAEAnV,OAAA,CAAAqG,KAAA,YAAA8O,SAAA,CAAA/O,EAAA;cACA4O,OAAA,CAAA1O,QAAA,CAAAD,KAAA;YAAA;cAAA8O,SAAA,CAAAtU,IAAA;cAEAmU,OAAA,CAAAhY,aAAA;cACAgY,OAAA,CAAAzN,MAAA,CAAAO,YAAA;cAAA,OAAAqN,SAAA,CAAA5O,MAAA;YAAA;YAAA;cAAA,OAAA4O,SAAA,CAAA7S,IAAA;UAAA;QAAA,GAAA2S,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}